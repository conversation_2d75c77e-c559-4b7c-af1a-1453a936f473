{"wiki_catalogs": [{"id": "cd0fae98-fc00-40b6-9300-013808b946c2", "repo_id": "d74cb73a-91ce-407a-844e-ffc8ee98d0af", "name": "项目概述", "description": "project-overview", "prompt": "创建关于AIChatBox项目的全面概述内容。解释该项目的目的、核心功能和整体架构。重点介绍它作为一个高度可定制的AI聊天应用的特点，包括聊天系统、记忆管理、多媒体播放、社交功能（如朋友圈）和个性化设置。描述项目的技术栈（纯原生JavaScript、HTML、CSS）以及无外部框架依赖的设计理念。说明其模块化设计和中心化状态管理（通过全局db对象）的实现方式。为初学者提供概念性介绍，同时为高级开发者提供技术细节，例如localStorage的使用、DOM操作和事件驱动通信机制。包含实际应用场景示例，并说明与其他类似项目的区别。", "parent_id": "", "order": 0, "progress_status": "completed", "dependent_files": "README.md", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-23T18:01:40.0453377+09:00", "gmt_modified": "2025-09-23T18:04:15.5881756+09:00", "raw_data": "WikiEncrypted:0MI1/XkBoMl0lTbK6t0Cn/+8FdvqrJ62ianMLvZj02elJtBUgH0Ns0veIx1WPtM9wwkxI+/XAqHLxJLlHB8wV7f+ZSFsWmYrjwHaKSwENxBxN1ucZGW6XlNSs2MVdc8oZYWUaEnmBgO5LAAirckn4+2ZzLI08TQW5qs/x6ZOKouRq6QSRGu/hxSc0jdRmEi2az7q6F2v7Z6YLxN76wg8wVw7xEj5yelM7xjEVXmSKrlRfkL5o0l/Pd6/us37SPR0xEjd/VPpQ2cqxSppjVSVLl5bRBFCWVlPDRhWRpB+bN4kpYbNwUvmG72G9z7yg6LzJjvGx1jaeKX+UgpI6z/4vSct3vFNYBbP8oRNUEDSqkz4qgg1N5FC2WP74PK/l1Pt2iP5hrWorl+FJ/OsTF71+KLcFVbmcEij7fAYo89RgrrVvJBrcYHRcxT3G5I32iIqdA2BlKLp9A1SPY4OCMO2L/fV0xxR/3vcVHV9TWDVFwi7qR9VFs7YEm6LCB/U0x3rpzwUacThtePahVjOBXw5PVgdqK3yXpKIKT/d89GfrCKFL3CYaPE7A32Awe/7B0503+1I30H8Ac605k690imKTn2rfCCfYapdobb3S9QrPvOqYD31xqEgoy1CtY8QNJOKHlLG3TOCupU+CcS01aa7ipeVlgIM5EQSV9o2RNO7CiIorak6zTGcCzSDmiXxR/JI1TBgH7Ei6DPCvBUQvKMy2z3ZMHYM8LZmFzrZQdCpHhSioZqWwYFgHIuCmCfMrU4qpoIXo1b+ZeGvQ90TXWaOeAN9Uq2LB2WvO33JpmEBkQaVaysbhlbhBNj2qhFiYB/LXCxh3glf3gr/haJbNtZTbzEzJ79VMTrSeI5Xn7gODb8UpmpUg8sqIoexTqnzy98HmSVMgJf2eioa1ir2Wg4lh/4K3ekTbyhrcmy0yVNlamepnrWXAQQlbppqupbAboGPhViBURRLF730+D2CAHjb0duZqg34nmYO6sNvLm/o+cApyPoprF8aEF9la9D7hKmjlL8YI8zO6i3rAjjEkClHTdgZQCL/Ymp4oVAK6nwkhB26I8LXtzKTCL/hEXMmWgmzlSo5xzVt0YvluYrRLqAQQuL+kElB0kCus8/1afPEr3t1fERnJMvt8lqjhnDnT/YViDkGo4uT5YwE8N7l60czRc7jbxbjRG6ILnOSsjyL4u+QxZdLHuhVaIu9vh+fUviyWL8gs1Bm0mY41eB7Pp58+hXd2+Rqr2pOAXDPEeCMxDJ5JS86EqbtsUlZ3m6e21hyI1RDnMNKbvYdc9jJXsn5TknucRVBizQYPhmpa1SeHzqcW8vWFMLNezUc2Qb2GmMw1lMNoOlMh32PcPFSI+UPoQ==", "layer_level": 0}, {"id": "6b7e5fd0-9663-45c4-9933-0b061f0f8d74", "repo_id": "d74cb73a-91ce-407a-844e-ffc8ee98d0af", "name": "技术架构", "description": "architecture-design", "prompt": "创建AIChatBox的技术架构文档。详细描述其模块化设计模式，包括setupXxx()函数如何初始化各个功能模块（如聊天室、API设置、音乐播放器等）。解释中心化状态管理模式，重点说明全局db对象的结构及其在各模块间共享数据的作用机制。阐述事件驱动的通信流程：用户交互 → 事件处理器 → 业务逻辑 → 更新db → 持久化到localStorage → UI更新。分析设计决策背后的权衡，例如选择原生JavaScript而非框架的原因。提供系统上下文图和组件交互图，说明CSS变量和响应式设计在主题定制中的应用。讨论性能考虑、可扩展性和维护性。", "parent_id": "", "order": 1, "progress_status": "completed", "dependent_files": "src/js/js.js,src/styles/css.css", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-23T18:01:40.0579616+09:00", "gmt_modified": "2025-09-23T18:05:23.1458831+09:00", "raw_data": "WikiEncrypted:EPw1VhZSv2AMLpYzHbCG5QXmatSKh1iukhkKRz99kcVnjJ7lfN5yi4omajiF3ql4j4cuTOKg/VLkjLAyqEu4ancpkaO7exP7ikfZWSDKDmBTFzCpQA/qGsoNcauswyPUi1F9A7U7wSZpgyqdAVva9R6CcHsJCRLoJzhYthBD8Z/bTHZQgypJeXzQc4q/IGvp4XJr/4YTDx9fUvlrdnIYvFOQZyLLNwSC3tEegaAtSjJIg5XML+WxIy4hLyKc2ARzawgDLfDdGC6Eo/Lj0O+KMcZF6BL+R53bnIhiXoE2hef1WdujUMR7S/FArFKafmP0t8mF3quPyNjJOgAsSn8mmH6/81VEASQBA8fNwz1+0xQodrRB/pUqdgwH9PthfN+f3X8GKU66Ut3iBfMMehROZoqv/LBMRbEdn8ENFGhxep8wdE8LGBEGbNHIvu60ARWUt7Ih+Zj5UaYYLqiR+gDKv4I3yLD/55rD6grb5GVHJox4g6mREvMmysZycLzUAh6M+H0fO9LnUiKdBjAjLwy6mCaTc4oVOzc/xi6lqgkSQ1r++zL+LsYc9SCxqWC1lrliK9+4vTZUgJ5lE390gsx8ntgZVxomvblsV0I47+mLCz+fZilmzY/Pcb9QvmEMXzMDwR+xaV+QeCKHe7pNlI6Q8x2cuMHamM5xIEoNTo7vQ8/aJmPFnuP/jXWumDThh4s0Px7Jb70/i1Yi0pfp7D7h78NUcBjdObKwChVYVDrgijlycVEhoYhbvVJ8D2KxGaAS1aUaex3wRcNwdzni+EQVgu7jtkrQTOpkYXHfbSLS/ziL7CCUJOOOuxTBNU4kCCGdgAwPxZNtt1871E39iewohObVszPISy7qZ41lAkTW/et6W8cwmn5WwuZz6UG3ntSZB2ItFN3msXhCidmy+yW781Hwh+bZ5HJdQ+tTzZ6pmlb2QntjR7y6zcUkim9g0qz+5+8RfRHKdc7QZ1UjX8EfVxHgWitkQZX55Y9JIzwCxGVk9fwLSyImAmJjEN/0cNxWoV9ul1AfS+ErZBLxabWxbNMY2bibXl79PcgiSKpVMhWrpkMm/pNJ97PrS+UOEd0vbzAOmqOUeFSokbJxU4ZWcb0DKcNInWzKxxgIsbX/GIoWRm7gemhKos23IfHg4JUzKr5thEIwNHq90Vk4JRC1hCi2bXsbDPq0XcZjp6BcFm18fAuu37tYIv+nS0mZeMcvmR2aTVUfKO0zPm13A7xOyqse8V+E+mQWEUDIxWmWBa4L1v6ky5oy2gJtb+kvBTI1odlBSNzENPRoihurlIgTkcd5QjrG4JieG0ztBmdXI+yb/YVh9P8l7RZ9shagv5E/JzyZoJufIUAMEpb3j11xUZgGtVmk/EPDXP4QbT0gYnc=", "layer_level": 0}, {"id": "e71b8ff7-ad51-4591-bec6-904ad8dc3ddf", "repo_id": "d74cb73a-91ce-407a-844e-ffc8ee98d0af", "name": "文件说明", "description": "file-structure", "prompt": "创建详细的文件说明文档，逐一解释每个核心文件的作用和内容。对于index.html，描述其作为主页面的结构组成，包括模态框定义和应用容器布局。对于js.js，深入分析其作为核心逻辑文件的角色，涵盖模块初始化函数、状态管理逻辑、DOM操作和事件监听器的实现。解释css.css中的全局样式规则、视觉风格定义以及如何通过CSS变量支持主题切换。说明README.md中包含的项目信息、使用说明和配置指南。为每个文件提供代码片段示例，展示关键部分的实现方式，并说明它们之间的依赖关系。", "parent_id": "", "order": 2, "progress_status": "completed", "dependent_files": "src/index.html,src/js/js.js,src/styles/css.css,README.md", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-23T18:01:40.0752998+09:00", "gmt_modified": "2025-09-23T18:05:58.2667297+09:00", "raw_data": "WikiEncrypted:l1O7XWjgWHyLZEgkeZtuwQwGbXeCsYOC2VRKwb3F1UtnOftxaJPRwDU8z62ZwSC6EA2IAsIShM60hOdXe8tIab4mJtN2vOafpoksiT2eb2rxkbstI7YLIi+fMqj5sdbXCg1eIkpB/uerj/WI25OkZKbjTiZ2VXOrovnLDNHciZ5X9fWIUIGGRuihBksmw/0UrP5yHpSAsytizubkPXO0Kl2trRXVi2j1pUQRxSlvFW1CnaXcVuipc8z96HMPDWnqKvUbzvbzBI/OtwKi4RsMEUoa83wNfq6yK9aT0LDucFs8JeDk9Via9z/SXFL/lxdh4Ps18Wpyn4qFHT/qMVz0ntMlYMo6ff8nN0gs08vPNwOMOXY7I72Lt/D79YB3hfuWGWXicpC/uWg4koKlaxFjBKBxxld9kzSFFhmh8PfJwrjDmV0xTcVtS8rOsdLv/VUOuU0tSgz8X+suk9OOIdsn4CeV9aKaBghFPzwEYcVW5+bOxMkNRlOyw9/gprh0VAdzHYtS6OH7U0Y64v/CSLq6Pc4ksBbQUt4FQ/JfflQTzXDYj+AcqXXzsaJPKRau+BNy5qM01fO1sJcE2r6mRt8Wz4ho6vtrLRXUAhzMHD4VIGxRErW9Loqzdq+O/4AVdpCdJzAVx4Ewh48U4bQJqw+3BwpgGT01h6ic8xsSHpJyO9eT1L4mwAGCfZoDTFASq738s79Y0XUfP4xE3c2l+J6joJpUmh4kHa+ymNynHuQNT5AvxMyh+nCdz5JT97cj1kGUXA+Jm9C6UYYz87lNWP8iIN2s4RLXP4PEKItqoeUaJTBWPu+2R4gMkft4fjlTEhdn1RqUV0SK4QW5JNOwEQDzZax3pBIyO+jfKVyadZv4MHLjctCYTfqxrPATh1Mz3P5xiL/B2FhkUxpPttOmwn9719/nT07NaKBvjGVyDrOyExtngCvACsYDrPHk91wgxMvmuyXPeFQx/OMSFUrxx0iAUNfkHyUe+1vst+3+X0xQ13PTb2os2TwtFmWFV8kG7h8C9AjnpN17zvDIYKaQuY8XTitJzDk2/QjwlIhz+1XeVEsxAJI7gkvi8//IP/kxK4lQNna5VHiAA6ApbNzjhfHrlL2evEZYNDEn6Mj5GNRfIj56j80IC5FwRF1tThjU/5tOODB4IXltnZTW1NtSnHj4DK5puKe49XT8QeSbII8XqCkHSd6wtOCj89XXrepO+TGPrRZltrPz7loUsgDvtzF/H1rpeAYPBnYSxYQCL9xcQMKZCQ+fHSuwgU3MWUdNCnvlXat9iHq3Ou/YsdUxM+ne/C1N+m1YjyJLO2GpIAbQ1XlhP0Lg7s1h9qyRu8LMektv", "layer_level": 0}, {"id": "92973c91-f58e-4f94-a3c4-e339e526bd1e", "repo_id": "d74cb73a-91ce-407a-844e-ffc8ee98d0af", "name": "核心功能", "description": "core-features", "prompt": "开发AIChatBox核心功能的详细文档。全面介绍聊天系统，包括消息发送/接收流程、多角色管理、消息类型（文本、图片、语音、转账）处理和多选操作机制。详细说明记忆系统的工作原理，涵盖记忆提取与注入、长期记忆管理策略。描述多媒体系统的实现，包括音乐播放器控制逻辑、歌单管理和动态岛显示机制。解释个性化设置功能，如主题、字体、壁纸的自定义实现。介绍社交功能模块，包括朋友圈发布、评论和动态展示。为每个功能提供实际使用示例、代码实现片段和常见问题解决方案。", "parent_id": "", "order": 3, "progress_status": "completed", "dependent_files": "src/js/js.js,src/index.html,src/styles/css.css", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-23T18:01:40.0796257+09:00", "gmt_modified": "2025-09-23T18:08:07.6574514+09:00", "raw_data": "WikiEncrypted:luoNp8LvFa7zGThvIT9T4gfD6KbzJyTAydbcRfVv0b4BXBAF9G/unmbHb0x4cm5f5JJeO16NqGe5C5EiggSB7tzU/FyelE8YRQDEuHq4YklTuPea8qDd36UqxqKBUoIYU90Ku31Em1PCgyemB2XQ7uNqibPnwBBdpcFFvL4wNSAzKYDQCAWPlV2hy477W8p47QZ3Vbg//3BYBBsLMHDhKzjCdat4ETNuUgMENVbOcF6wONrnjFjwbTyH7tOqE7yaiXHM8GPZfQJr2HIPywEsD4QAnyawKJOpbx95/j/AXxd2saBxByRSxEgr2ydSC8UuoU3vmV7zP8ZFuZsdqbkq8BP6cjK3puo3XDXN6qbD0p9hFMIw8ham28dJN6tNpoBtpCQUabG8Yqt440OYrLW0O9DWsth2o8XSaYOFjhJ9BxZOty6Oc8SXsRlicFkikgPmxmTQ3X32V/7AkjOxyy3IqLV4xlIDRTTKlMISvCbvgHxqjwIVCGHcPctZ4EQXXJFuzUQNvLzl1MBA0hX50YmSVpRRaw+LL3wyLbAo+66WugyQGkyRk2fiRNKKXKkZ8uhOBOIIrmmtZixh4tM/DW5jUbbjyJHov5f533Mq4ajiwAicGYgOevYCR90zd1/sfEobkDkhun4XhMMwCiv9lo+NZ9e8cm/Y72BZz1TLQa/f59EnNtwAvvvHALHYy++iUS10ujLYoAwSQiJaUiMC2or0heVtqEVwl2z1/jLNDsbAZBC6gtpJe6bYPcH1tkyaLqRzyqOJhqC5rWzraOmqrruQgKoCDNi+dEMBb0ur0kan6iBCEvNCWV1AcX5FD5yA4S07sFPDk0V0ExSBN8y0Cemt5nitgsX2W0vGqKqTrHp8/HaSo/fJHk3/QZve2MjTqrwUIwwRU8E4aYRYWBK6ksc7RpDPddXyrcEkY1TNDeERPBbkxxnzVW6HrZZbBXxD2Kh49gqy1SpPvOB8oHUCbkwh7GUdl7sYmdsnwB0aOjU5hYAgrlH341tsCSMCawpMC2E9Y74mPNfzwGESKV/xOZjnGg1Xax8SPfHp4EFvzAmgGifnfG/scx3IqiVJQDxHzcadhXcMcCIDjQ//nyRLyN+kCN9otN4qQfasOoJiw6DcOC1DmUrAdbus8GnEZXARFxa9eA30zC40WF9SSjlj8gIYpLA+3p6rgEwyCrBKD1S/vKb9YJrX1UGoag1J3imnFIH4s4vHygEXcPkthtVyJ1/lkLGkBjmDiGiYndKkHT4r/9cXRKTBuQlj6OKubpJ4QlIbViB8p5T6dtNvyUFyUmt+FmtLDiWdjEfUYWSaKfIRdvcHXmXWTCgTsVIwmJxRrgT6", "layer_level": 0}, {"id": "0016a948-160a-40ca-96d8-441d1dfc3aff", "repo_id": "d74cb73a-91ce-407a-844e-ffc8ee98d0af", "name": "数据模型", "description": "data-model", "prompt": "创建AIChatBox的数据模型文档。全面描述db对象的结构，详细定义其各个属性（如characters、groups、userProfiles、apiProfiles、memoryEntries、musicPlaylist）的数据类型、字段含义和相互关系。解释这些数据如何通过saveData()函数持久化到localStorage中，包括序列化和反序列化过程。说明数据验证规则和业务逻辑约束。提供数据模型的示意图，展示实体间的关系。讨论数据访问模式、性能考虑和存储限制。包含示例数据片段，帮助理解实际存储格式，并说明数据生命周期管理和潜在的迁移策略。", "parent_id": "", "order": 4, "progress_status": "completed", "dependent_files": "src/js/js.js", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-23T18:01:40.0842465+09:00", "gmt_modified": "2025-09-23T18:08:18.9088593+09:00", "raw_data": "WikiEncrypted:klcgW2PbPxJambbKMzvFtzKMOkAiMygw2jhMWheBgWjabVsg22USnmOBp3LIxuc8YTM5U6Vo1ZyRK7NYXb50e4DPmDI1LXjrqnLxYmBoh3rD/jwLj6ZreZLihiKWEYjKg7O2jVg8IZfw31YymBo57KrlZBitvlv4biYBy7eiXRUCc5B9OenbuBEfGALQ+C+d0Vc0RnQk7jFHu4qGgxsixUlWMU6xPJfZMfQsktLCGhUT4li6by8MiVzehzyqzjeq90ep1gjgDtwW3Phu1bA9sMafTW0MZ8PPUyOrSnt0YXrlg3fFdH4DVm235U4HKeOYZbdgV7o3IrBjHkzDO7zu0zxGXlSpAPC9icyxgHAs85XC0wpoQ1N+70X7DrwX7ZS0KiXAja2DVdhTSWGJS7vjePpFUG8Wpzqe/hWnnhcSitgkjeZVfS0uvXhuu3XcoMW2AhfL9MPc1yCu4YPWZP6XXAA4iOhbC4RS/GCk7XpXX2SANA3pbLyQ0LfoOhVCknesYyGKWE0G9DelJY0YEROkgNFatLViMFy6DFObW/BhNJsdEKORHvpDotqYlrZdkwkgBysxAYZWbaWNGY1fBjrH0tbk7b5zqgEeeIPU4i1qa9FG59Do6tsFeUstOhIkupJNQUx7qsbVg12oPlort1bpzEg7pQ+eboB8b2VTJJ5c/mf9MmOXRCl73xGTZO5Ou1Pn0yPKHecIZcfWThXwMsVc//U/o+Esten7FQWJtMXHOHRZnoDTSSc6tDodCC9nseiGQo0s97f1u3FDXVVEfrTOAX+6YQWIiJWtt1M2ekTaJw9wmFqAn3ivzmrk5zEEQJtgITlAf3wqs6kTZxwXiV7x+oRhR/Bhq+GsBUjdBldkKxZbu4aK0aGVFWx0jtBfFKuScdOb6I7nZR27ZJM2y25ILleyXQvmV+0GnNSPqubOSy4gasbcMRsWigLlWwIbPOW6e8PfS5Gy3Iqrs4ECGOLoOHoc2VCVPTm1bzPGx3vSsoW5RatNansknE7zzNaFYCwXFZD+NfvTYyIOfBp++12n5BkJzLViDxq2IReg8OpmVZWLagCk4jxapWESDfUF+h9Z5UTI2VWSpdu+JdRMgZAJsoVM0x2N/UzaFTE/pHjHCwaqL6i4Wn3shRhK7FK7iU/oqvtLRfvwdhgguk2M+AEsGGXWBKqkB/2aouhAEhJH97mbb+AuBq3w+3BNxZpA7ZGtxSnKeJJVezRtQBSeL2zWXv0zleJtPyTWpE63dsq+Lz+l7yEIB4gcdfcgRd/TsHzA", "layer_level": 0}, {"id": "e7b70078-faf3-413a-b335-db962feb9cab", "repo_id": "d74cb73a-91ce-407a-844e-ffc8ee98d0af", "name": "开发指南", "description": "development-guide", "prompt": "编写AIChatBox的开发指南，为新贡献者提供清晰的入门路径。首先介绍开发环境搭建步骤，包括必要的浏览器兼容性要求和调试工具推荐。详细说明代码结构组织方式，解释模块化JavaScript设计的最佳实践。指导开发者如何添加新功能，例如通过创建新的setupXxx()函数来扩展应用。提供修改现有模块的具体示例，强调对全局db对象的正确操作方式。包含编写单元测试的建议（尽管当前项目未实现），以及调试技巧和常见错误排查方法。最后，给出代码风格指南和提交规范，确保代码库的一致性和可维护性。", "parent_id": "", "order": 5, "progress_status": "completed", "dependent_files": "src/js/js.js,src/index.html,src/styles/css.css", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-23T18:01:40.0931734+09:00", "gmt_modified": "2025-09-23T18:14:01.3789996+09:00", "raw_data": "WikiEncrypted:F3QgleoEfoy16cQggYe9C/ucQxiwh0okVA48AmODs5RKNS8ZgNngqmnHSFcG/1UaJxx+3dUn+EkMTpYUsOg40lUz9EJaxbjXnov1GuDc2ZI5MShC+Z8Wq22NFZunpiv1Hs8SO0X/Oz7wQUHH9p99Pni2uKTxMSm6RrN/OLuxCU8vOgwXwn5kAOt3KLl0N16mTUoQbKTZ4Hbyvm8wI+okhAln1qcYZM9ALgJbvY560RpHD7G/KkYpY9iva8TJ2Odjf0eIEaI86WmnOoi+FwYU/ncd7DN482y72F5i+AS1da5pi8oFVJ4ACR8z5TDhkz8iZ7lt/6ihcUm5tf2Lv4KWEZoFpwTEFV5ErX15Lmt6WBPR6qYM/hIwpuyAFmJtzFmGu/2kN4d0s6aa7YepREqdGl/i72TEd4FS6+51/1pXLVqNcCMYagOQdrjYNFMqxkcO8tAK8t2NDhu/W8Y3BlEUjsRqfPKL0ssoHHH9NShrVZSn5v5eIa19tmfnQNkJFMgVRVxw8JLTzy4hf+DadzmVUak/gZLZiEl0F7ik63bdqbJvVwt4ozGCYRSjVJgrx4+xhWGcRTtzFO2wPZHYALycsVbQ9I8V8PRPi2pDUtdBoBdgEDcW9eOODweQ4meDrj4qOzGO1p8Hvq4acel278xfrItJSi2Jh3pYMnCErkI0DWoZebAz5J9dz+gEr6EzJArPHzhPIRCR7/3VlY+2Q0jEPwMGKobBDmuRAGLduqCXWgRQU76OEFXFrBGJ3YJ7gKhZpuihzkzPJVTEruW3SnDegKa+2n1GajuBHYTU15cPBYguoAmkN0UNmCiMRbZXX2WXLtbpYwlVfYncJkJ6lRZSDT9L5EJkAPr2U3eEmhpoI1c+QyGzLP3tMf7G6p/nsFzYCJSljvGj84L/BIlIY4jS6LY2WBwMC2+6MoN/FjLXHyUKJqP8NkME2i8IoLoSTPKUXLETEXIaDqw8Hr01hyiewclOpRrG3CNkPh1LieNQ/Pvdr0nQPGmgdyqhdVn7OoCj4gkkt7KXas4oW6+nkic5BehgvFKoD83/2TFnCGv6lydGEzsgHS2sdEV4G4m7Laj8T/DQm+ZO0S+emqfs2LbP9tpxF88PVM1LoAQG903G9lTnffPpo54RF64DwCc6VG8hRC9YSaIy2ItBkBO68cNaUE5skSleh0b14QoQZ04WMOithcQ8TfIU8dGLOt5D2c3HQfNlDwPCMH1AIRyU0F8DbPdwjuVSSScHVbMkao+8JcgByuBpW+Cwpwce8IOHlejExeJgeFblAqtjb4r4x6CWuV+JTY05arsmVU5RBx/tnbA+qCXTgYAv2eqeWdVC4DgW3EE28HWnMkbFoc/ooSQaJVVYe43sx+EpcdPplycwk4s=", "layer_level": 0}], "wiki_items": [{"catalog_id": "cd0fae98-fc00-40b6-9300-013808b946c2", "content": "", "title": "项目概述", "description": "project-overview", "extend": "{}", "progress_status": "completed", "repo_id": "d74cb73a-91ce-407a-844e-ffc8ee98d0af", "workspace_path": "", "id": "28b1821e-1d2f-46cd-a061-a01b953c180b", "gmt_create": "2025-09-23T18:04:15.5831131+09:00", "gmt_modified": "2025-09-23T18:04:15.5894451+09:00"}, {"catalog_id": "6b7e5fd0-9663-45c4-9933-0b061f0f8d74", "content": "", "title": "技术架构", "description": "architecture-design", "extend": "{}", "progress_status": "completed", "repo_id": "d74cb73a-91ce-407a-844e-ffc8ee98d0af", "workspace_path": "", "id": "24e901d7-31c7-4a98-a43d-344f07fb2f5d", "gmt_create": "2025-09-23T18:05:23.1394212+09:00", "gmt_modified": "2025-09-23T18:05:23.1475239+09:00"}, {"catalog_id": "e71b8ff7-ad51-4591-bec6-904ad8dc3ddf", "content": "", "title": "文件说明", "description": "file-structure", "extend": "{}", "progress_status": "completed", "repo_id": "d74cb73a-91ce-407a-844e-ffc8ee98d0af", "workspace_path": "", "id": "105a22f5-dcef-49d7-b6f2-f1836382a26d", "gmt_create": "2025-09-23T18:05:58.2617892+09:00", "gmt_modified": "2025-09-23T18:05:58.2688074+09:00"}, {"catalog_id": "92973c91-f58e-4f94-a3c4-e339e526bd1e", "content": "", "title": "核心功能", "description": "core-features", "extend": "{}", "progress_status": "completed", "repo_id": "d74cb73a-91ce-407a-844e-ffc8ee98d0af", "workspace_path": "", "id": "3066808a-54e7-40d8-bea5-03f72559ca94", "gmt_create": "2025-09-23T18:08:07.6506538+09:00", "gmt_modified": "2025-09-23T18:08:07.6597488+09:00"}, {"catalog_id": "0016a948-160a-40ca-96d8-441d1dfc3aff", "content": "", "title": "数据模型", "description": "data-model", "extend": "{}", "progress_status": "completed", "repo_id": "d74cb73a-91ce-407a-844e-ffc8ee98d0af", "workspace_path": "", "id": "40f6028a-cb0f-4eec-b826-07f4cc45cc2d", "gmt_create": "2025-09-23T18:08:18.9026419+09:00", "gmt_modified": "2025-09-23T18:08:18.9106632+09:00"}, {"catalog_id": "e7b70078-faf3-413a-b335-db962feb9cab", "content": "", "title": "开发指南", "description": "development-guide", "extend": "{}", "progress_status": "completed", "repo_id": "d74cb73a-91ce-407a-844e-ffc8ee98d0af", "workspace_path": "", "id": "0207b0de-1242-4c0c-b9f3-8621c952bddc", "gmt_create": "2025-09-23T18:14:01.3732481+09:00", "gmt_modified": "2025-09-23T18:14:01.3796144+09:00"}], "wiki_overview": {"content": "<blog>\n# AIChatBox 项目分析报告\n\n## 1. 项目介绍\n\n### 项目目的\nAIChatBox 是一个功能丰富的AI聊天应用，旨在为用户提供一个高度可定制、沉浸式的虚拟聊天体验。它不仅支持基础的聊天功能，还集成了音乐播放、日记记录、记忆管理、朋友圈等社交功能，构建了一个完整的虚拟生活生态系统。\n\n### 核心目标\n- 提供个性化的AI聊天体验\n- 实现多维度的用户自定义设置\n- 构建虚拟角色的长期记忆系统\n- 集成多媒体和社交功能，增强沉浸感\n\n### 目标用户\n- 喜欢与AI进行深度互动的用户\n- 需要情感陪伴和心理支持的个体\n- 对虚拟角色养成感兴趣的玩家\n- 希望通过AI记录生活点滴的用户\n\n## 2. 技术架构\n\n### 组件分解\n项目采用典型的前端单页应用(SPA)架构，主要由以下核心组件构成：\n\n```mermaid\nflowchart TD\n    A[客户端] --> B[UI界面]\n    A --> C[业务逻辑]\n    A --> D[数据管理]\n    B --> E[屏幕组件]\n    C --> F[功能模块]\n    D --> G[本地存储]\n    E --> H[聊天界面]\n    E --> I[设置界面]\n    E --> J[多媒体界面]\n    F --> K[聊天系统]\n    F --> L[记忆系统]\n    F --> M[音乐系统]\n    F --> N[日记系统]\n    G --> O[localStorage]\n```\n\n### 设计模式\n项目采用了模块化设计模式，通过`setupXxx()`系列函数初始化各个功能模块。数据管理采用中心化存储模式，所有应用状态都存储在全局`db`对象中，并通过`saveData()`函数持久化到`localStorage`。\n\n### 系统关系\n各组件之间通过事件驱动和状态共享进行通信。UI组件监听用户交互事件，业务逻辑组件处理数据并更新UI，数据管理组件负责数据的持久化和同步。\n\n### 数据流\n```mermaid\nflowchart TD\n    A[用户交互] --> B[事件处理器]\n    B --> C[业务逻辑处理]\n    C --> D[更新db状态]\n    D --> E[saveData到localStorage]\n    D --> F[更新UI显示]\n    G[页面加载] --> H[loadData从localStorage]\n    H --> I[初始化db状态]\n    I --> J[渲染UI]\n```\n\n## 3. 关键实现\n\n### 主要入口点\n- `src/index.html`：应用的主HTML文件，定义了所有屏幕和模态框的结构\n- `src/js/js.js`：应用的核心JavaScript文件，包含所有业务逻辑\n- `src/styles/css.css`：应用的样式文件，定义了全局样式和组件样式\n\n### 核心模块\n- **聊天系统** (`src/js/js.js`)：处理消息发送、接收、显示等核心功能\n- **记忆系统** (`src/js/js.js`)：管理AI角色的记忆，支持记忆的提取和注入\n- **设置系统** (`src/js/js.js`)：提供丰富的个性化设置选项\n- **多媒体系统** (`src/js/js.js`)：支持音乐播放、图片上传等功能\n\n### 配置方法\n- **主题配置**：通过`db.themeColor`和`db.wallpaper`进行全局主题和壁纸设置\n- **字体配置**：通过`db.fontUrl`设置自定义字体\n- **API配置**：通过`db.apiProfiles`管理多个API配置文件\n- **日记配置**：通过`db.diarySettings`管理日记本的显示和频率设置\n\n### 外部依赖\n- **localStorage**：用于持久化存储应用数据\n- **Web API**：使用FileReader、Audio等Web API处理文件和音频\n- **外部图片资源**：从网络加载各种图标和背景图片\n\n### 集成点\n- **API集成**：支持多种AI服务提供商（NewAPI、DeepSeek、Claude、Gemini）\n- **文件系统集成**：支持从本地上传图片、音乐等文件\n- **通知系统**：实现顶部通知和动态岛功能\n- **多媒体集成**：集成音乐播放器和语音消息功能\n\n### 组件关系\n```mermaid\ngraph LR\n    A[HomeScreen] --> B[ChatListScreen]\n    A --> C[MusicScreen]\n    A --> D[DiaryBookshelfScreen]\n    A --> E[MemoryCoreScreen]\n    A --> F[ApiSettingsScreen]\n    A --> G[BeautifyScreen]\n    B --> H[ChatRoomScreen]\n    H --> I[ChatSettingsSidebar]\n    H --> J[CollectionScreen]\n    C --> K[PlaylistManageScreen]\n    D --> L[DiaryViewScreen]\n    E --> M[EditMemoryScreen]\n    F --> N[ApiSettingsForm]\n    G --> O[FontSettingsForm]\n```\n\n## 4. 关键特性\n\n### 功能概述\nAIChatBox 提供了丰富的功能，包括：\n- 多角色聊天管理\n- 智能记忆系统\n- 个性化主题和字体\n- 音乐播放和播放列表管理\n- 日记自动生成功能\n- 朋友圈动态\n- 语音和视频通话\n- 转账和红包功能\n\n### 实现亮点\n- **动态岛功能**：实现了类似iPhone的动态岛设计，用于显示音乐播放状态\n- **记忆核心**：通过`memory-injection-prompt`和`memory-extraction-prompt`实现AI记忆的智能管理\n- **自定义CSS**：允许用户通过自定义CSS代码深度定制聊天界面\n- **主动搭话**：AI可以根据设置主动发起对话，增强互动性\n- **日记系统**：自动记录聊天内容生成日记，支持手动生成\n\n### 功能架构\n```mermaid\nstateDiagram-v2\n    [*] --> HomeScreen\n    HomeScreen --> ChatListScreen: 点击聊天图标\n    HomeScreen --> MusicScreen: 点击音乐图标\n    HomeScreen --> DiaryBookshelfScreen: 点击日记本图标\n    HomeScreen --> MemoryCoreScreen: 点击记忆图标\n    HomeScreen --> ApiSettingsScreen: 点击项圈图标\n    HomeScreen --> BeautifyScreen: 点击美化图标\n    ChatListScreen --> ChatRoomScreen: 选择聊天\n    ChatRoomScreen --> ChatSettingsSidebar: 点击设置\n    ChatRoomScreen --> CollectionScreen: 点击收藏\n    MusicScreen --> PlaylistManageScreen: 点击管理歌单\n    DiaryBookshelfScreen --> DiaryViewScreen: 选择日记\n    ApiSettingsScreen --> ApiSettingsForm: 编辑配置\n    BeautifyScreen --> FontSettingsForm: 字体设置\n    HomeScreen --> CallScreen: 视频通话\n    CallScreen --> [*]\n```\n\nSources:\n- [README.md](c:\\01_workspace\\01_PJ\\AIChatBox\\README.md)\n- [index.html](c:\\01_workspace\\01_PJ\\AIChatBox\\src\\index.html)\n- [js.js](c:\\01_workspace\\01_PJ\\AIChatBox\\src\\js\\js.js)\n- [css.css](c:\\01_workspace\\01_PJ\\AIChatBox\\src\\styles\\css.css)\n</blog>", "gmt_create": "2025-09-23T17:58:42.580047+09:00", "gmt_modified": "2025-09-23T17:58:42.580047+09:00", "id": "54c24363-410f-4c91-8235-f0823da914c9", "repo_id": "d74cb73a-91ce-407a-844e-ffc8ee98d0af", "workspace_path": ""}, "wiki_readme": {"content": "No readme file", "gmt_create": "2025-09-23T17:55:36.3049173+09:00", "gmt_modified": "2025-09-23T17:55:36.3049173+09:00", "id": "c027401c-8093-4af7-8458-8750c063dbe0", "repo_id": "d74cb73a-91ce-407a-844e-ffc8ee98d0af", "workspace_path": ""}, "wiki_repo": {"id": "d74cb73a-91ce-407a-844e-ffc8ee98d0af", "workspace_path": "", "name": "AIChatBox", "progress_status": "completed", "wiki_present_status": "COMPLETED", "optimized_catalog": "\".\\n├── src\\n│   ├── js\\n│   │   └── js.js\\n│   ├── styles\\n│   │   └── css.css\\n│   └── index.html\\n└── README.md\\n\"", "current_document_structure": "WikiEncrypted: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", "catalogue_think_content": "WikiEncrypted: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", "recovery_checkpoint": "wiki_generation_completed", "last_commit_id": "944cb409487711a34fe219dcf4cca1ee77310231", "last_commit_update": "2025-09-23T17:52:53+09:00", "gmt_create": "2025-09-23T17:55:25.9671006+09:00", "gmt_modified": "2025-09-23T18:14:01.3996351+09:00", "extend_info": "{\"language\":\"zh\",\"active\":true,\"branch\":\"main\",\"shareStatus\":\"\",\"server_error_code\":\"\",\"cosy_version\":\"\"}"}}