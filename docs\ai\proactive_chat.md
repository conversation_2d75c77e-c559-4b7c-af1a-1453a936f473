# AI功能文档：主动聊天

## 1. 功能概述

### 核心目标
赋予AI角色在长时间未互动后主动发起对话的能力，打破用户必须先发言的限制，从而提升聊天的真实感和沉浸感。

### 典型应用场景
在用户与AI角色最后一次对话的几个小时后，AI角色可能会根据当前时间（例如，早上）或之前的话题，主动发送一条消息，如“早上好！”或“说起来，我们昨天聊到的那部电影，我又想到了些事……”。

## 2. 实现细节

### 完整逻辑流程
1.  系统通过 `setInterval` 定期（每5分钟）触发 `proactiveChatScheduler` 函数。
2.  `proactiveChatScheduler` 遍历所有私聊角色和群聊。
3.  对于每个开启了“主动搭话”(`proactiveChat.enabled === true`)的聊天：
    a. 检查最后一条消息是否为用户发送的。如果AI是最后发言者，则跳过，避免连续骚扰。
    b. 计算自最后一条消息以来的时间差。
    c. 根据用户为该聊天设置的搭话频率（`high`, `medium`, `low`），计算出一个随机的时间阈值。
    d. 如果时间差超过了这个阈值，则判定需要主动发起聊天。
4.  系统调用 `generateProactiveChatPrompt()` (私聊) 或 `generateGroupProactiveChatPrompt()` (群聊) 来构建一个专门用于发起对话的提示词。
5.  该提示词包含了当前时间、最近的聊天记录摘要、朋友圈互动素材等信息，并要求AI据此自然地开启一个新话题。
6.  调用 `getAiReply(prompt)` 函数，将此提示词发送给AI模型。
7.  AI返回1-4条简短、口语化的开场白消息。
8.  系统处理这些消息，将其存入聊天历史，并为该聊天增加未读角标。
9.  如果用户当前不在该聊天界面，系统会弹出一个顶部通知 (`showTopNotification`)，提示用户收到了新消息。

### 输入输出规范
-   **输入**:
    -   `proactiveChatScheduler` 隐式使用 `db` 对象中的聊天设置、历史记录和时间戳。
    -   AI模型的输入是一个专门构建的、用于开启话题的系统提示词。
-   **输出**:
    -   AI模型输出1-4条符合格式的聊天消息。
    -   最终在UI上表现为AI发送的新消息和一条新消息通知。

### 异常处理
-   如果AI返回空内容或格式不正确，则不会发送任何消息。
-   后续的API请求错误由 `getAiReply` 的通用异常处理机制负责。

## 3. 代码位置

-   **调度器入口**: `src/js/js.js#L5409-5479` (函数 `proactiveChatScheduler`)
-   **私聊提示词生成**: `src/js/js.js#L5481-5524` (函数 `generateProactiveChatPrompt`)
-   **群聊提示词生成**: `src/js/js.js#L5526-5549` (函数 `generateGroupProactiveChatPrompt`)

## 4. 提示词模板

提示词是动态生成的，其核心指令硬编码在 `generate...Prompt` 函数中。

### 私聊开场白提示词 (`generateProactiveChatPrompt`)

该提示词指示AI根据上下文自然地开启新话题。

```
你正在扮演角色“{{character.realName}}”，人设是：{{character.persona}}。
现在是 {{currentTime}}。
距离你们的上次对话（{{lastMessageTime}}）已经过去了一段时间。

你需要根据当前时间和你们最近的聊天内容，非常自然地开启一个新的话题或继续之前的话题，主动发消息给“{{userProfile.name}}”。

---
最近的聊天记录：
{{lastMessages}}
---
朋友圈互动素材：
{{momentContext}}
---

请你主动发送一条消息，让对话继续下去。你的消息必须非常自然、符合人设，就像一个真实的人在思考后重新发起对话。
【重要】请参考当前时间和上次对话的时间差来决定你的开场白。例如，如果是第二天早上，可以说“早上好”；如果只是过了几个小时，可以接着之前的话题说，或者引用朋友圈的互动来开启新话题。
【禁止】使用“在吗？”、“你好”等干巴巴的开场白。

【要求】
1.  **消息拆分**: 你必须生成 **2到4条** 简短的、独立的聊天消息。
2.  **口语化**: 每条消息都应该非常口语化、自然，就像真实的线上聊天一样，避免书面语。
3.  **格式严格**: 每一条独立的消息都必须用一个完整的 `[{{character.realName}}的消息：...]` 包裹。
4.  **内容连贯**: 这几条消息在内容上应该是连贯的，共同构成一个完整的话题开启或问候。
```

## 5. 存储方案

与聊天回复功能类似，这条关于主动聊天的指令是硬编码在 `generateProactiveChatPrompt` 函数内的。

### 实施建议
建议将此规则模板作为可配置项，而不是硬编码。可以将其存储在 `db.promptSettings.proactive_chat_prompt` 中，允许高级用户通过设置界面进行修改，以调整AI主动聊天的风格和行为。