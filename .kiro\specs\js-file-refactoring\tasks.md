# 实施计划

## 概述

将 AIChatBox 项目中超过8000行的单体 js.js 文件按功能模块进行自动化拆分，建立清晰的模块化架构。采用分阶段实施策略，每个阶段完成后进行验证确认，确保拆分过程的稳定性和准确性。

## 任务列表

### 第一阶段：建立模块化基础架构

- [x] 1. 创建基础目录结构


  - 创建 src/js/ 下的所有目标目录（utils, core, ui, chat, ai, media, communication, content, customization）
  - 确保目录结构与设计文档一致
  - _需求: 1.1, 2.1_



- [ ] 2. 创建主入口文件
  - 创建 src/js/main.js 作为新的入口文件
  - 在 main.js 中添加 `import './js.js';`
  - 保持原有的初始化逻辑完全不变


  - _需求: 10.1, 10.2_

- [x] 3. 更新 HTML 文件引用


  - 将 src/index.html 中的 `<script src="./js/js.js"></script>` 更改为 `<script type="module" src="./js/main.js"></script>`
  - 验证应用仍能正常启动和运行
  - _需求: 10.3, 10.4_

- [ ] 4. 验证第一阶段基础架构
  - 确认应用完全正常工作
  - 检查所有功能无回归
  - 确保控制台无错误
  - _需求: 12.1, 12.2_

### 第二阶段：拆分常量定义

- [x] 5. 拆分常量定义文件


  - 从 js.js 中提取所有全局常量到 utils/constants.js
  - 包括 colorThemesV2, defaultIcons, MESSAGES_PER_PAGE 等常量
  - 包括所有 SVG 图标常量
  - 包括所有全局变量声明
  - 保持原有格式和注释不变
  - _需求: 1.2, 3.1_

- [x] 6. 更新主入口文件导入


  - 在 main.js 中添加 `import './utils/constants.js';`
  - 确保导入顺序在 js.js 之前
  - _需求: 10.1, 10.2_

- [x] 7. 从原文件中移除已拆分内容


  - 从 js.js 中删除已拆分到 constants.js 的内容
  - 确保删除完整，无遗留
  - _需求: 3.1, 3.2_



- [ ] 8. 验证第二阶段拆分结果
  - 确认应用完全正常工作
  - 检查常量定义功能正常
  - 验证无功能回归
  - _需求: 12.1, 12.2_

### 第三阶段：拆分通用工具函数

- [ ] 9. 拆分通用工具函数
  - 从 js.js 中提取通用工具函数到 utils/common-utils.js
  - 包括 setVhVariable, getEl, showToast, pad, createContextMenu, removeContextMenu
  - 保持原有实现和注释不变
  - _需求: 1.3, 3.2_

- [ ] 10. 更新主入口文件导入
  - 在 main.js 中添加 `import './utils/common-utils.js';`
  - 确保导入顺序正确
  - _需求: 10.1, 10.2_

- [ ] 11. 从原文件中移除已拆分内容
  - 从 js.js 中删除已拆分到 common-utils.js 的内容
  - 确保删除完整，无遗留
  - _需求: 3.1, 3.2_

- [ ] 12. 验证第三阶段拆分结果
  - 确认应用完全正常工作
  - 检查工具函数功能正常
  - 验证无功能回归
  - _需求: 12.1, 12.2_

### 第四阶段：拆分颜色处理工具

- [ ] 13. 拆分颜色处理工具
  - 从 js.js 中提取颜色处理函数到 utils/color-utils.js
  - 包括 adjustColor, lightenRgba, getContrastYIQ
  - 保持原有实现和注释不变
  - _需求: 1.4, 3.3_

- [ ] 14. 更新主入口文件导入
  - 在 main.js 中添加 `import './utils/color-utils.js';`
  - 确保导入顺序正确
  - _需求: 10.1, 10.2_

- [ ] 15. 从原文件中移除已拆分内容
  - 从 js.js 中删除已拆分到 color-utils.js 的内容
  - 确保删除完整，无遗留
  - _需求: 3.1, 3.2_

- [ ] 16. 验证第四阶段拆分结果
  - 确认应用完全正常工作
  - 检查颜色处理功能正常
  - 验证无功能回归
  - _需求: 12.1, 12.2_

### 第五阶段：拆分核心系统模块

- [ ] 17. 拆分应用初始化模块
  - 从 js.js 中提取初始化函数到 core/app-init.js
  - 包括 init, injectHTML 函数
  - 保持原有实现和注释不变
  - _需求: 4.1, 4.2_

- [ ] 18. 更新主入口文件导入
  - 在 main.js 中添加 `import './core/app-init.js';`
  - 确保导入顺序正确
  - _需求: 10.1, 10.2_

- [ ] 19. 从原文件中移除已拆分内容
  - 从 js.js 中删除已拆分到 app-init.js 的内容
  - 确保删除完整，无遗留
  - _需求: 4.1, 4.2_

- [ ] 20. 拆分数据存储管理模块
  - 从 js.js 中提取数据操作函数到 core/data-storage.js
  - 包括 loadData, saveData 函数
  - 保持原有实现和注释不变
  - _需求: 4.3, 4.4_

- [ ] 21. 更新主入口文件导入
  - 在 main.js 中添加 `import './core/data-storage.js';`
  - 确保导入顺序正确
  - _需求: 10.1, 10.2_

- [ ] 22. 从原文件中移除已拆分内容
  - 从 js.js 中删除已拆分到 data-storage.js 的内容
  - 确保删除完整，无遗留
  - _需求: 4.3, 4.4_

- [ ] 23. 验证第五阶段拆分结果
  - 确认应用完全正常工作
  - 检查核心系统模块函数完整性
  - 验证应用仍能正常初始化
  - 确认数据加载和保存功能正常
  - _需求: 4.5, 12.3_

### 第三阶段：界面管理模块拆分

- [ ] 9. 拆分屏幕导航模块
  - 从 js.js 中提取屏幕切换函数到 ui/screen-navigation.js
  - 包括 switchScreen 函数
  - 保持原有实现和注释不变
  - _需求: 5.1, 5.2_

- [ ] 10. 拆分主屏幕管理模块
  - 从 js.js 中提取主屏幕相关函数到 ui/home-screen.js
  - 包括 setupHomeScreen, updateClock, applyWallpaper, applyHomeScreenMode
  - 保持原有实现和注释不变
  - _需求: 5.3, 5.4_

- [ ] 11. 拆分弹窗对话框模块
  - 从 js.js 中提取弹窗相关函数到 ui/modal-dialogs.js
  - 包括 adjustContentPadding, togglePlusMenu, toggleStickerModal
  - 保持原有实现和注释不变
  - _需求: 5.5, 5.6_

- [ ] 12. 验证第三阶段拆分结果
  - 检查界面管理模块函数完整性
  - 验证屏幕切换功能正常
  - 确认主屏幕和弹窗功能正常
  - _需求: 5.7, 12.4_

### 第四阶段：聊天系统核心模块拆分

- [ ] 13. 拆分聊天列表模块
  - 从 js.js 中提取聊天列表相关函数到 chat/chat-list.js
  - 包括 setupQQApp, renderChatList, updateHeaderActions
  - 保持原有实现和注释不变
  - _需求: 6.1, 6.2_

- [ ] 14. 拆分联系人管理模块
  - 从 js.js 中提取联系人相关函数到 chat/contacts.js
  - 包括 setupAddCharModal, setupContactsScreen, renderContactsList
  - 保持原有实现和注释不变
  - _需求: 6.3, 6.4_

- [ ] 15. 拆分聊天室模块
  - 从 js.js 中提取聊天室相关函数到 chat/chat-room.js
  - 包括 setupChatRoom, updateSendButtonState, handlePatPat
  - 保持原有实现和注释不变
  - _需求: 6.5, 6.6_

- [ ] 16. 拆分消息处理模块
  - 从 js.js 中提取消息处理相关函数到 chat/message-handling.js
  - 包括 renderPlusMenu 等消息相关函数
  - 保持原有实现和注释不变
  - _需求: 6.7, 6.8_

- [ ] 17. 验证第四阶段拆分结果
  - 检查聊天系统模块函数完整性
  - 验证聊天列表和联系人功能正常
  - 确认聊天室和消息处理功能正常
  - _需求: 6.9, 12.5_

### 第五阶段：AI集成模块拆分

- [ ] 18. 拆分API管理模块
  - 从 js.js 中提取API相关函数到 ai/api-management.js
  - 包括 getAiReply, processStream, setupApiSettingsApp, setupApiManager, renderApiProfileList, loadApiProfileToForm
  - 保持原有实现和注释不变
  - _需求: 7.1, 7.2_

- [ ] 19. 拆分记忆核心模块
  - 从 js.js 中提取记忆相关函数到 ai/memory-core.js
  - 包括 extractAndStoreMemory, setupMemoryCoreApp, setupMemoryCoreSettingsApp, loadMemorySettings, renderMemoryCoreList
  - 保持原有实现和注释不变
  - _需求: 7.3, 7.4_

- [ ] 20. 拆分提示词生成模块
  - 从 js.js 中提取提示词生成函数到 ai/prompt-generation.js
  - 包括 generatePrivateSystemPrompt, generateGroupSystemPrompt
  - 保持原有实现和注释不变
  - _需求: 7.5, 7.6_

- [ ] 21. 拆分AI调度模块
  - 从 js.js 中提取AI调度相关函数到 ai/ai-scheduling.js
  - 包括 automaticPostScheduler, proactiveChatScheduler, generateProactiveChatPrompt, generateGroupProactiveChatPrompt
  - 保持原有实现和注释不变
  - _需求: 7.7, 7.8_

- [ ] 22. 验证第五阶段拆分结果
  - 检查AI集成模块函数完整性
  - 验证API管理和记忆核心功能正常
  - 确认AI调度和提示词生成功能正常
  - _需求: 7.9, 12.6_

### 第六阶段：媒体系统模块拆分

- [ ] 23. 拆分图片处理模块
  - 从 js.js 中提取图片处理函数到 media/image-handling.js
  - 包括 compressImage 函数
  - 保持原有实现和注释不变
  - _需求: 8.1, 8.2_

- [ ] 24. 拆分音乐播放器模块
  - 从 js.js 中提取音乐播放器相关函数到 media/music-player.js
  - 包括所有音乐播放相关的15个函数
  - 保持原有实现和注释不变
  - _需求: 8.3, 8.4_

- [ ] 25. 拆分语音消息模块
  - 从 js.js 中提取语音消息相关函数到 media/voice-messages.js
  - 包括 calculateVoiceDuration, sendMyVoiceMessage, setupVoiceMessageSystem
  - 保持原有实现和注释不变
  - _需求: 8.5, 8.6_

- [ ] 26. 拆分动态岛模块
  - 从 js.js 中提取动态岛相关函数到 media/dynamic-island.js
  - 包括 setupDynamicIsland, updateDynamicIsland
  - 保持原有实现和注释不变
  - _需求: 8.7, 8.8_

- [ ] 27. 验证第六阶段拆分结果
  - 检查媒体系统模块函数完整性
  - 验证图片处理和音乐播放功能正常
  - 确认语音消息和动态岛功能正常
  - _需求: 8.9, 12.7_

### 第七阶段：通信功能模块拆分

- [ ] 28. 拆分通话系统模块
  - 从 js.js 中提取通话相关函数到 communication/call-system.js
  - 包括所有通话相关的10个函数
  - 保持原有实现和注释不变
  - _需求: 9.1, 9.2_

- [ ] 29. 拆分表情包系统模块
  - 从 js.js 中提取表情包相关函数到 communication/stickers.js
  - 包括 sendSticker, setupStickerSystem, parseAndAddStickers, renderStickerGrid
  - 保持原有实现和注释不变
  - _需求: 9.3, 9.4_

- [ ] 30. 拆分钱包系统模块
  - 从 js.js 中提取钱包相关函数到 communication/wallet.js
  - 包括 sendMyTransfer, setupWalletSystem, handleReceivedTransferClick, respondToTransfer
  - 保持原有实现和注释不变
  - _需求: 9.5, 9.6_

- [ ] 31. 拆分礼物和位置系统模块
  - 从 js.js 中提取礼物相关函数到 communication/gifts.js
  - 从 js.js 中提取位置相关函数到 communication/location.js
  - 保持原有实现和注释不变
  - _需求: 9.7, 9.8_

- [ ] 32. 验证第七阶段拆分结果
  - 检查通信功能模块函数完整性
  - 验证通话和表情包功能正常
  - 确认钱包、礼物和位置功能正常
  - _需求: 9.9, 12.8_

### 第八阶段：内容管理和个性化模块拆分

- [ ] 33. 拆分世界书系统模块
  - 从 js.js 中提取世界书相关函数到 content/world-book.js
  - 包括 setupWorldBookApp, renderWorldBookList
  - 保持原有实现和注释不变
  - _需求: 9.10, 9.11_

- [ ] 34. 拆分朋友圈模块
  - 从 js.js 中提取朋友圈相关函数到 content/moments.js
  - 包括 setupMomentsApp, renderMomentsFeed, loadMomentsSettings, saveMomentsSettings
  - 保持原有实现和注释不变
  - _需求: 9.12, 9.13_

- [ ] 35. 拆分日记系统模块
  - 从 js.js 中提取日记相关函数到 content/diary.js
  - 包括所有日记相关的10个函数
  - 保持原有实现和注释不变
  - _需求: 9.14, 9.15_

- [ ] 36. 拆分收藏系统模块
  - 从 js.js 中提取收藏相关函数到 content/collections.js
  - 包括 setupFileAndCollectionSystem, renderCollections
  - 保持原有实现和注释不变
  - _需求: 9.16, 9.17_

- [ ] 37. 拆分个性化模块
  - 从 js.js 中提取主题相关函数到 customization/themes.js
  - 从 js.js 中提取美化相关函数到 customization/beautify.js
  - 从 js.js 中提取字体相关函数到 customization/fonts.js
  - 保持原有实现和注释不变
  - _需求: 9.18, 9.19_

- [ ] 38. 验证第八阶段拆分结果
  - 检查内容管理和个性化模块函数完整性
  - 验证世界书、朋友圈、日记功能正常
  - 确认收藏和个性化功能正常
  - _需求: 9.20, 12.9_

### 第九阶段：群聊和聊天设置模块拆分

- [ ] 39. 拆分群聊功能模块
  - 从 js.js 中提取群聊相关函数到 chat/group-chat.js
  - 包括所有群聊相关的7个函数
  - 保持原有实现和注释不变
  - _需求: 6.10, 6.11_

- [ ] 40. 拆分聊天设置和我的页面模块
  - 从 js.js 中提取聊天设置相关函数到 chat/chat-settings.js
  - 从 js.js 中提取我的页面相关函数到 chat/me-screen.js
  - 保持原有实现和注释不变
  - _需求: 6.12, 6.13_

- [ ] 41. 验证第九阶段拆分结果
  - 检查群聊和聊天设置模块函数完整性
  - 验证群聊功能正常
  - 确认聊天设置和我的页面功能正常
  - _需求: 6.14, 12.10_

### 最终阶段：完成拆分和清理

- [ ] N-2. 移除原始 js.js 导入
  - 从 main.js 中移除 `import './js.js';`
  - 确保所有功能都已通过其他模块导入
  - _需求: 10.1, 10.2_

- [ ] N-1. 进行完整功能验证
  - 测试所有主要功能模块
  - 验证聊天、AI、音乐、通话等核心功能
  - 确认没有功能回归
  - 检查控制台是否有错误
  - _需求: 12.1, 12.2, 12.3, 12.4_

- [ ] N. 清理和文档更新
  - 备份原始 js.js 文件（重命名为 js.js.backup）
  - 更新项目文档
  - 记录拆分过程中的注意事项
  - _需求: 11.1, 11.2, 11.3_

## 实施注意事项

1. **渐进式拆分**: 每次只拆分一小部分内容，确保系统始终可用
2. **三步循环**: 每个拆分都遵循"拆分→导入→移除"的三步循环
3. **立即验证**: 每完成一个拆分循环立即验证系统功能
4. **保持原状**: 所有函数保持原有实现、格式、注释不变
5. **导入顺序**: 确保 main.js 中的导入顺序正确，避免依赖问题
6. **完整移除**: 从原文件移除内容时确保完整，避免重复定义
7. **ES6模块**: 使用 ES6 模块语法，确保浏览器兼容性
8. **错误处理**: 如发现问题立即停止，分析原因后再继续

## 拆分模式

每个拆分任务都遵循以下模式：
1. **创建新文件** - 将指定函数/常量复制到新的模块文件
2. **更新导入** - 在 main.js 中添加新模块的导入
3. **移除原内容** - 从 js.js 中删除已拆分的内容
4. **验证功能** - 确认应用仍然完全正常工作

## 成功标准

- 所有原有功能正常工作
- 代码结构清晰，易于维护
- 没有功能回归或新增bug
- 模块间依赖关系清晰
- 文件大小合理，便于管理