
# 数据模型

<cite>
**本文档引用的文件**   
- [js.js](file://src/js/js.js)
</cite>

## 目录
1. [数据模型](#数据模型)
2. [数据持久化](#数据持久化)
3. [数据验证与业务逻辑](#数据验证与业务逻辑)
4. [数据模型示意图](#数据模型示意图)
5. [数据访问与性能](#数据访问与性能)
6. [示例数据](#示例数据)
7. [数据生命周期与迁移](#数据生命周期与迁移)

## 数据模型

AIChatBox 应用的核心数据存储在一个名为 `db` 的全局 JavaScript 对象中。该对象在应用启动时通过 `loadData()` 函数从浏览器的 `localStorage` 中加载，并在应用运行期间持续维护。当数据发生变化时，会通过 `saveData()` 函数将其序列化并持久化回 `localStorage`。

`db` 对象是一个包含多个集合（collections）的复合数据结构，每个集合都代表了应用中的一个核心实体。以下是其主要属性的详细定义：

- **characters**: 一个对象数组，存储用户创建的“伙伴”角色。每个角色对象包含 `id`、`realName`（真名）、`remarkName`（备注/昵称）、`persona`（人设）、`avatar`（头像URL）、`history`（聊天历史）等属性。`history` 数组存储了与该角色的所有消息记录。
- **groups**: 一个对象数组，存储用户创建的“群聊”信息。每个群聊对象包含 `id`、`name`（群聊名称）、`avatar`（群头像）、`members`（成员列表）和 `history`（群聊历史）等属性。`members` 数组中的每个成员对象都关联到一个 `characters` 中的角色。
- **userProfiles**: 一个对象数组，存储用户为“我”创建的不同身份卡。每个身份卡包含 `id`、`name`（姓名）、`avatar`（头像）、`persona`（人设）、`walletBalance`（钱包余额）和 `region`（地区）等属性。用户可以在与不同角色聊天时切换身份。
- **apiProfiles**: 一个对象数组，存储用户配置的AI服务提供商（如Gemini、Claude等）的API信息。每个配置包含 `id`、`name`（配置名称）、`provider`（服务商）、`url`（API地址）、`key`（密钥）和 `model`（选择的模型）等属性。
- **memoryEntries**: 一个对象数组，存储从聊天记录中提取的“记忆”。每个记忆条目有 `id`、`type`（类型，如'character'或'global'）、`characterId`（关联的角色ID）、`topic`（主题）和 `content`（详细内容）等属性。这些记忆用于在后续对话中注入上下文，使AI角色更具连贯性。
- **musicPlaylist**: 一个对象数组，存储用户添加的音乐播放列表。每个音乐条目包含 `id`、`title`（标题）、`artist`（艺术家）、`url`（音频文件URL）、`albumArt`（专辑封面URL）和 `isLiked`（是否喜欢）等属性。
- **worldBooks**: 一个对象数组，存储用户创建的“爪印书”条目。每个条目有 `id`、`name`（条目名称）、`content`（详细内容）和 `position`（注入位置，'before'或'after'）等属性。这些设定可以被注入到特定角色的系统提示词中。
- **moments**: 一个对象数组，存储“朋友圈”动态。每条动态包含 `id`、`characterId`（发布者ID）、`content`（动态内容）、`imageUrl`（图片URL）、`musicUrl`（音乐URL）、`timestamp`（时间戳）、`likes`（点赞者ID列表）和 `comments`（评论列表）等属性。
- **collections**: 一个对象数组，存储用户收藏的消息。每条收藏记录包含 `id`、`sourceChatId`（来源聊天ID）、`sourceMessageId`（来源消息ID）和 `timestamp`（收藏时间）等属性。
- **diaries**: 一个对象，其键为角色ID，值为包含该角色日记条目的对象。每个日记对象包含 `entries`（日记条目数组）和 `background`（背景图）等属性。日记条目包含 `date`、`weather` 和 `content` 等字段。

**Section sources**
- [js.js](file://src/js/js.js#L100-L150)

## 数据持久化

`db` 对象的数据持久化是通过 `saveData()` 和 `loadData()` 两个核心函数实现的，它们负责在内存中的 JavaScript 对象和浏览器的 `localStorage` 之间进行双向同步。

### 序列化过程
当调用 `saveData()` 函数时，会执行以下序列化过程：
1.  **数据准备**：首先创建一个 `dbToSave` 对象，它是当前 `db` 对象的一个浅拷贝。这确保了原始数据在序列化过程中不会被意外修改。
2.  **数据过滤**：对于 `musicPlaylist` 数组，会过滤掉所有 `isLocal` 属性为 `true` 的条目。这表明应用在保存时只保留通过URL引用的音乐，而不会尝试保存本地文件路径。
3.  **序列化**：使用 `JSON.stringify()` 方法将 `dbToSave` 对象转换为一个JSON格式的字符串。
4.  **持久化**：将生成的JSON字符串通过 `localStorage.setItem()` 方法，以键名 `'gemini-chat-app-db'` 存储到浏览器的 `localStorage` 中。

### 反序列化过程
当应用初始化时，`loadData()` 函数会被调用，执行反序列化过程：
1.  **数据读取**：使用 `localStorage.getItem('gemini-chat-app-db')` 从 `localStorage` 中读取之前保存的JSON字符串。
2.  **解析**：使用 `JSON.parse()` 将JSON字符串解析回一个JavaScript对象。
3.  **数据合并与初始化**：将解析出的数据与一个包含所有默认值和结构的 `defaultDb` 对象进行合并。这确保了即使 `localStorage` 中的数据不完整或缺失某些字段，应用也能正常运行。例如，如果 `db` 对象中没有 `characters` 数组，它将被初始化为空数组。
4.  **数据迁移**：函数中包含数据迁移逻辑，用于处理旧版本数据结构的更新。例如，如果旧数据中存在 `apiSettings` 对象，它会被转换为新的 `apiProfiles` 数组格式，并删除旧的 `apiSettings` 字段。
5.  **数据验证**：对所有预期为数组的属性（如 `characters`, `groups` 等）进行检查，如果它们不是数组类型，则强制将其初始化为空数组，以防止后续操作出错。

这个过程确保了用户数据在页面刷新或应用重启后能够被完整恢复。

**Section sources**
- [js.js](file://src/js/js.js#L100-L150)

## 数据验证与业务逻辑

AIChatBox 在数据模型上实施了严格的验证规则和业务逻辑约束，以保证数据的完整性和用户体验的一致性。

### 数据验证规则
- **必填字段**：在创建或编辑角色、身份卡、API配置、爪印书条目等实体时，关键字段如 `name`、`remarkName`、`url`、`key`、`content` 等都被标记为 `required`，前端表单会进行验证，防止用户提交空值。
- **数据类型**：代码中通过 `parseFloat()` 和 `parseInt()` 等函数确保 `walletBalance`（钱包余额）和 `maxMemory`（最大记忆轮数）等字段为数值类型。
- **数组类型保护**：`loadData()` 函数中包含一个关键的保险机制，即遍历所有已知的数组键（如 `characters`, `groups` 等），并检查它们是否为数组。如果不是，则将其重置为空数组。这防止了因数据损坏或迁移失败导致的运行时错误。
- **唯一性**：虽然没有显式的数据库约束，但通过为每个实体（如角色、群聊、记忆条目）生成唯一的 `id`（通常基于时间戳），间接保证了主键的唯一性。

### 业务逻辑约束
- **角色与身份的关联**：每个 `characters` 对象都通过 `userProfileId` 字段关联到一个 `userProfiles` 中的身份卡。当用户在聊天设置中切换身份时，会更新该字段。
- **群聊成员管理**：`groups` 对象中的 `members` 数组存储了群成员信息。成员可以是 `characters` 中的角色（通过 `originalCharId` 关联），也可以是仅存在于该群聊中的独立成员。管理员可以添加、移除成员或更改其昵称。
- **记忆提取与注入**：存在一个复杂的业务逻辑，即自动从聊天历史中提取关键信息并存储为 `memoryEntries`。同时，这些记忆会通过 `memorySettings.injectionPrompt` 模板，在生成AI回复时被注入到系统提示词中，从而影响AI的行为。
- **朋友圈互动**：`moments` 动态支持点赞和评论。`likes` 和 `comments` 数组分别存储了点赞者和评论者的ID，实现了社交互动功能。
- **数据生命周期**：用户可以清空聊天记录、删除角色或群聊。删除操作会级联删除相关的聊天历史、日记条目等，确保数据一致性。

**Section sources**
- [js.js](file://src/js/js.js#L100-L150)

## 数据模型示意图

```mermaid
erDiagram
  CHARACTERS {
    string id PK
    string realName
    string remarkName
    string persona
    string avatar
    string userProfileId FK
    string baseTheme
    string chatBg
    boolean isPinned
    boolean isBlocked
    int unreadCount
  }

  GROUPS {
    string id PK
    string name
    string avatar
    string chatBg
    boolean isPinned
    int unreadCount
  }

  USER_PROFILES {
    string id PK
    string name
    string avatar
    string persona
    float walletBalance
    string region
  }

  API_PROFILES {
    string id PK
    string name
    string provider
    string url
    string key
    string model
  }

  MEMORY_ENTRIES {
    string id PK
    string type
    string characterId FK
    string topic
    string content
    timestamp timestamp
  }

  MUSIC_PLAYLIST {
    string id PK
    string title
    string artist
    string url
    string albumArt
    boolean isLiked
  }

  WORLD_BOOKS {
    string id PK
    string name
    string content
    string position
  }

  MOMENTS {
    string id PK
    string characterId FK
    string content
    string imageUrl
    string musicUrl
    timestamp timestamp
  }

  COLLECTIONS {
    string id PK
    string sourceChatId
    string sourceMessageId
    timestamp timestamp
  }

  DIARIES {
    string characterId PK
    array entries
    string