# 项目概述

<cite>
**本文档引用的文件**   
- [README.md](file://README.md)
- [index.html](file://src/index.html)
- [js.js](file://src/js/js.js)
- [css.css](file://src/styles/css.css)
</cite>

## 目录
1. [项目简介](#项目简介)
2. [核心功能](#核心功能)
3. [技术架构](#技术架构)
4. [模块化设计与状态管理](#模块化设计与状态管理)
5. [技术栈与设计理念](#技术栈与设计理念)
6. [实际应用场景](#实际应用场景)
7. [与其他项目的区别](#与其他项目的区别)

## 项目简介

AIChatBox 是一个高度可定制化的AI聊天应用，旨在为用户提供一个沉浸式、个性化的虚拟伙伴交互体验。该项目以纯原生JavaScript、HTML和CSS构建，不依赖任何外部框架，确保了轻量化和高性能。其设计灵感来源于对个性化数字伴侣的追求，允许用户创建和管理多个虚拟角色，每个角色都拥有独特的性格、记忆和互动方式。

项目通过localStorage实现数据持久化，利用DOM操作和事件驱动机制构建响应式用户界面。全局`db`对象作为中心化状态管理的核心，存储了所有聊天记录、用户配置、多媒体信息和自定义设置。AIChatBox不仅是一个聊天工具，更是一个集成了社交、娱乐和个性化表达的综合平台。

**Section sources**
- [README.md](file://README.md#L1-L2)
- [index.html](file://src/index.html#L1-L10)
- [js.js](file://src/js/js.js#L583-L610)

## 核心功能

AIChatBox 提供了一系列丰富的核心功能，使其区别于传统的聊天应用。

### 聊天系统
应用的核心是其强大的聊天系统，支持一对一私聊和群组聊天。用户可以与创建的虚拟伙伴进行自然语言对话，AI会根据预设的人设和记忆进行回复。聊天界面支持多种消息类型，包括文本、图片、语音、视频通话、转账、红包、礼物、位置和文件分享。消息气泡支持高度自定义，用户可以为每个聊天对象设置不同的主题颜色和CSS样式。

### 记忆管理
记忆核心是AIChatBox的智能之源。系统允许用户手动创建全局记忆，或设置AI在特定消息轮数后自动从对话历史中提取关键信息。这些记忆被无缝整合到AI的角色人格中，使其能够记住用户的生日、偏好、重要事件等，并在相关情境下自然地提及，极大地增强了对话的连贯性和真实感。记忆的注入和提取提示词均可在设置中自定义。

### 多媒体播放
内置的“汪汪音乐”播放器提供完整的音乐播放功能。用户可以添加本地或在线音乐文件到播放列表，支持顺序播放、随机播放和单曲循环。播放器界面美观，包含专辑封面、播放进度条、播放控制按钮和歌词显示。动态岛（Dynamic Island）功能会在后台播放音乐时在屏幕顶部显示迷你播放控件，提供无缝的音乐体验。

### 社交功能（朋友圈）
“朋友圈”功能模拟了社交网络的动态发布。用户可以发布包含文字和图片的动态，AI伙伴会根据设定的频率自动发布自己的动态。其他用户（包括AI）可以对动态进行点赞和评论，形成一个虚拟的社交圈。用户可以完全自定义朋友圈的头像、背景和发布频率。

### 个性化设置
个性化是AIChatBox的一大亮点。用户可以：
- **美化主屏幕**：更换壁纸、设置签名、自定义应用图标。
- **自定义字体**：通过URL加载并应用自定义字体。
- **调整主题**：更改全局主题颜色。
- **管理表情包**：添加、删除和批量导入自定义表情。
- **日记本**：AI伙伴会根据聊天内容自动生成日记，用户可以像翻书一样浏览。

**Section sources**
- [js.js](file://src/js/js.js#L5700-L5871)
- [js.js](file://src/js/js.js#L6877-L6978)
- [js.js](file://src/js/js.js#L5385-L5705)
- [js.js](file://src/js/js.js#L7802-L7896)
- [index.html](file://src/index.html#L48-L74)

## 技术架构

AIChatBox采用单页应用（SPA）的架构模式，通过JavaScript动态切换不同的`div`屏幕来实现页面导航，避免了页面刷新，提供了流畅的用户体验。

```mermaid
graph TB
A[用户界面] --> B[事件监听]
B --> C[DOM操作]
C --> D[更新全局db]
D --> E[localStorage持久化]
E --> F[数据加载]
F --> A
G[API调用] --> H[AI回复生成]
H --> D
```

**Diagram sources**
- [index.html](file://src/index.html#L48-L74)
- [js.js](file://src/js/js.js#L562-L586)

## 模块化设计与中心化状态管理

项目采用了清晰的模块化设计。`js.js`文件通过`injectHTML()`函数动态注入各个屏幕的HTML结构，将UI与逻辑分离。每个功能模块（如聊天、音乐、朋友圈）都有独立的事件处理函数和渲染函数，例如`setupChatRoomApp()`、`setupMusicApp()`和`setupMomentsApp()`，这些函数在`DOMContentLoaded`事件后被调用，初始化各自的功能。

中心化状态管理通过全局`db`对象实现。`db`是一个包含所有应用数据的JavaScript对象，其结构在`loadData()`函数中定义，包括`characters`（角色）、`groups`（群组）、`userProfiles`（用户身份）、`moments`（朋友圈）、`memoryEntries`（记忆条目）、`musicPlaylist`（音乐列表）等。`saveData()`和`loadData()`函数负责将`db`对象与`localStorage`进行同步，确保数据在页面刷新后不丢失。这种设计简化了数据流，使得任何模块都可以方便地访问和修改全局状态。

**Section sources**
- [js.js](file://src/js/js.js#L583-L649)
- [js.js](file://src/js/js.js#L562-L586)

## 技术栈与设计理念

AIChatBox的技术栈完全基于Web标准：HTML5用于结构，CSS3用于样式和动画，原生JavaScript用于逻辑和交互。项目不使用任何前端框架（如React、Vue），这使得代码更加透明，易于理解和修改，也减少了包体积。

其设计理念是“无外部框架依赖”和“高度可定制”。通过纯原生技术，项目避免了框架的复杂性和潜在的性能开销。同时，通过暴露`db`对象和提供自定义CSS、字体、图标等功能，项目赋予了用户极大的自由度来塑造应用的外观和行为。`localStorage`的使用确保了数据的本地化和隐私性，而事件驱动的通信机制（如`switchScreen`函数和各种`click`事件监听器）保证了组件间的松耦合和高效通信。

**Section sources**
- [js.js](file://src/js/js.js#L562-L586)
- [css.css](file://src/styles/css.css#L1-L50)

## 实际应用场景

AIChatBox适用于多种场景：
- **情感陪伴**：用户可以创建一个性格温和的虚拟伙伴，用于倾诉心事，AI会根据记忆提供有同理心的回应。
- **学习助手**：创建一个知识渊博的AI老师，通过聊天系统进行问答学习，记忆功能可以记录学习进度和难点。
- **娱乐社交**：与多个AI角色创建群聊，模拟一个虚拟社交圈，朋友圈功能可以分享日常，音乐播放器可以一起听歌。
- **创意写作**：利用日记本功能，AI会根据与用户的互动自动生成角色日记，为故事创作提供素材。

## 与其他项目的区别

与市面上其他AI聊天应用相比，AIChatBox的主要区别在于其**极致的可定制性和本地化**。大多数应用是封闭的，用户只能在有限的选项内进行设置。而AIChatBox允许用户从UI到数据流进行深度定制，甚至可以通过自定义CSS完全改变应用的视觉风格。此外，数据完全存储在用户的本地设备上，不依赖云端服务器，保护了用户隐私。其模块化的设计和纯原生的技术栈也使其成为一个优秀的学习和二次开发项目。