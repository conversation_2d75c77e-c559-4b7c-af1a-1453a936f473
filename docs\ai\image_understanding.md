# AI功能文档：图像理解

## 1. 功能概述

### 核心目标
赋予AI角色“视觉”能力，使其能够理解和响应用户在聊天中发送的图片内容。

### 典型应用场景
用户在与AI角色的聊天中上传一张图片（例如，一张风景照、一张自拍或一张梗图）。AI能够识别图片内容，并生成一段相关的、符合对话上下文的文字回复。

## 2. 实现细节

### 完整逻辑流程
1.  用户通过文件选择器 (`image-upload-input`) 选择一张图片。
2.  `setupImageUpload` 函数中的 `change` 事件监听器被触发。
3.  为了优化传输和API成本，图片首先被客户端的 `compressImage` 函数进行压缩（限制最大宽高和调整质量）。
4.  压缩成功后，调用 `sendImageMessage(compressedUrl)` 函数，参数为压缩后图片的 `base64` 或 `blob` URL。
5.  `sendImageMessage` 函数执行以下操作：
    a. 创建一个 `role: 'user'` 的消息对象。
    b. 该消息对象的文本内容固定为 `[用户发送了一张图片]`。
    c. 关键一步：在该消息对象中添加一个 `imageData` 属性，其值为图片的URL。
    d. 将这个包含图片信息的用户消息添加到聊天记录中，并立即在界面上显示出来。
    e. **立即调用 `getAiReply()` 函数，触发AI回复。**
6.  `getAiReply()` 函数在准备发送给AI供应商的API请求时，会检查聊天历史中的最新消息。
7.  当它发现最新的用户消息包含 `imageData` 属性时，它会构建一个多模态（multimodal）的请求体。这个请求体同时包含了文本（`[用户发送了一张图片]`）和图片数据。
8.  支持视觉功能的AI模型（如GPT-4 Vision, Gemini Pro Vision）接收到这个请求后，会结合图片内容和文本提示进行理解，并生成回复。
9.  后续的响应处理流程与普通的文本聊天回复完全相同，通过 `processStream` 函数进行解析和显示。

### 输入输出规范
-   **输入**: 用户通过UI选择的本地图片文件。
-   **输出**: AI模型生成的、与图片内容相关的文本回复。

### 异常处理
-   如果在图片压缩环节失败，会通过 `showToast` 提示用户“图片处理失败，请重试”。
-   后续的API请求错误由 `getAiReply` 函数的通用异常处理机制负责。

## 3. 代码位置

-   **图片上传与压缩处理**: `src/js/js.js#L4077-4093` (函数 `setupImageUpload`)
-   **图片消息发送与AI调用触发**: `src/js/js.js#L4096-4118` (函数 `sendImageMessage`)
-   **多模态请求体构建**: `src/js/js.js#L3649-3658` (在 `getAiReply` 函数内部的逻辑)

## 4. 提示词模板

对图像理解的指示是包含在私聊的整体系统提示词中的。

-   **模板位置**: 硬编码于 `generatePrivateSystemPrompt` 函数内部。
-   **具体指令**:
    ```
    - [用户发送了一张图片]：我给你发送了一张图片。你拥有视觉能力，请描述图片内容并据此回应。
    ```
-   这条规则明确告知AI，当它收到内容为 `[用户发送了一张图片]` 的消息时，意味着同时附带了一张图片，并要求它利用其视觉能力来完成回复。

## 5. 存储方案

### 当前方案
与聊天回复功能类似，这条关于图像理解的指令是硬编码在 `generatePrivateSystemPrompt` 函数内的。

### 实施建议
强烈建议将此规则作为可配置项，而不是硬编码。

-   **具体方案**:
    1.  将这条指令从JS代码中移除。
    2.  将其合并到 `db.promptSettings.private_chat_rules` 模板中（如 `chat_reply_generation.md` 文档中所建议的）。
    3.  这样，用户就可以通过修改 `localStorage` 中的这个模板来调整AI对图片的行为。例如，可以改成“请简要描述图片”或“请关注图片中的情感并回复”，从而实现更灵活的控制。