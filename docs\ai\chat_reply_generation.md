# AI功能文档：聊天回复生成

## 1. 功能概述

### 核心目标
为应用中的AI角色（在私聊和群聊中）生成符合其人设、上下文感知且交互方式丰富的回复。这是整个应用的核心AI功能。

### 典型应用场景
-   **私聊**: 用户向AI角色发送消息后，AI根据其人设、记忆和当前对话上下文进行回复。
-   **群聊**: 用户在群聊中发言后，AI同时扮演群内所有AI角色，生成一段模拟多人讨论的、真实且混乱的对话。
-   **特殊事件响应**: AI对用户的特殊操作（如发送图片、礼物、转账、红包等）做出相应的、智能的反应。

## 2. 实现细节

### 完整逻辑流程
1.  用户的行为（如发送消息 `sendMessage`、发送图片 `sendImageMessage`）最终会触发对 `getAiReply()` 函数的调用。
2.  `getAiReply()` 首先判断当前是私聊还是群聊。
3.  根据聊天类型，分别调用 `generatePrivateSystemPrompt()` 或 `generateGroupSystemPrompt()` 来动态构建一个极其详细的“系统提示词”。
4.  **提示词构建**: 这个系统提示词是AI行为的“宪法”，它包含了：
    -   严格的行为规则和输出格式要求。
    -   当前日期和时间。
    -   共享的全局记忆 (`db.memoryEntries`)。
    -   角色专属的个人记忆和人设 (`character.persona`, `character.memory`)。
    -   世界观设定 (`db.worldBooks`)。
    -   近期朋友圈动态 (`db.moments`)。
    -   处理各种特殊指令（如接收礼物、回复转账、发起视频通话、记录日记、生成心声等）的详细说明。
5.  `getAiReply()` 获取最近的N条聊天记录（数量由 `chat.maxMemory` 控制）。
6.  函数根据 `db.activeApiProfileId` 确定当前使用的API供应商（如 `gemini` 或 `openai`）和配置（URL, Key, Model）。
7.  它将系统提示词和聊天记录组装成符合特定供应商API要求的请求体（Payload）。
8.  向AI供应商的API端点发起一个流式（streaming）`fetch` 请求。
9.  `processStream()` 函数负责接收并处理返回的数据流。它会实时解析收到的文本片段。
10. **响应解析**: `processStream` 将完整的AI回复分解为多条消息和指令。它可以处理：
    -   普通文本消息。
    -   特殊指令，如 `[角色A撤回了一条消息]`、`[角色B领取了红包]`。
    -   内部状态，如 `<heart_voice>` (角色心声) 和 `<diary_entry>` (日记)。
    -   富文本模块，如 `HTML Module`。
11. 解析出的每条消息或指令被处理后，通过 `addMessageBubble()` 添加到聊天界面，并调用 `saveData()` 更新数据库状态。

### 输入输出规范
-   **输入**:
    -   主要输入是用户的操作和完整的应用状态数据库 `db`。
    -   `getAiReply` 函数本身可以接受 `customPrompt` 参数用于非聊天任务（如记忆提取）。
-   **输出**:
    -   AI的输出是一个文本流。
    -   经过 `processStream` 解析后，会产生多条聊天消息对象和状态更新操作，最终渲染到UI并持久化存储。

### 异常处理
-   `getAiReply` 包含完整的 `try...catch` 机制。
-   如果API配置缺失，会提示用户前往设置。
-   如果API请求失败（如网络错误、认证失败），会通过 `showToast` 向用户显示错误信息。
-   对损坏的流式响应数据有一定的容错能力。

## 3. 代码位置

-   **核心AI调用函数**: `src/js/js.js#L3532-3698` (函数 `getAiReply`)
-   **流式响应处理**: `src/js/js.js#L3701-3893` (函数 `processStream`)
-   **私聊提示词生成**: `src/js/js.js#L3115-3286` (函数 `generatePrivateSystemPrompt`)
-   **群聊提示词生成**: `src/js/js.js#L3288-3432` (函数 `generateGroupSystemPrompt`)

## 4. 提示词模板

提示词是动态生成的，其核心规则和指令格式目前硬编码在 `generate...SystemPrompt` 函数中。以下是两个核心场景的完整提示词内容结构。

### 私聊提示词 (`generatePrivateSystemPrompt`)

该提示词为AI角色提供了详细的背景信息和行为准则。

```
你正在一个名为"汪汪小屋"的线上聊天软件中扮演一个角色。请严格遵守以下规则：
核心规则：
A. 当前时间：现在是 {{currentTime}}。
B. 纯线上互动，严禁提出任何关于线下见面的建议。

--- 记忆核心 (所有角色都应记住的共享信息) ---
{{globalMemories}}

--- 关于我们的记忆 ---
{{characterMemory}}

--- 朋友圈动态摘要 (请注意区分发布者是你还是我) ---
{{momentsContext}}

角色和对话规则：
{{worldBooksBefore}}
1. 你的角色名是：{{character.realName}}。我的称呼是：{{userProfile.name}}。你的当前状态是：{{character.status}}。
2. 你的角色设定是：{{character.persona}}
{{worldBooksAfter}}
3. 关于我的人设：{{userProfile.persona}}
4. 我的消息中可能会出现特殊格式，请根据其内容和你的角色设定进行回应：
    - [{{userProfile.name}}的消息：xxx]：这是我发送的普通文本消息。
    - [我在回复“xxx: xxx”时说：xxx]：这是我引用了别人消息的回复。
    - [用户发送了一张图片]：我给你发送了一张图片。你拥有视觉能力，请描述图片内容并据此回应。
    - [{{userProfile.name}}的表情包：xxx]：我给你发送了一个名为xxx的表情包。你只需要根据表情包的名字理解我的情绪或意图并回应，不需要真的发送图片。
    - [{{userProfile.name}}送来的礼物：xxx]：我给你送了一个礼物，xxx是礼物的描述。
    - [{{userProfile.name}}的语音：xxx]：我给你发送了一段内容为xxx的语音。
    - [{{userProfile.name}}发来的照片/视频：xxx]：我给你分享了一个描述为xxx的照片或视频。
    - [{{userProfile.name}}给你转账：xxx元；备注：xxx]：我给你转了一笔钱。
    - [{{userProfile.name}}送出的红包：xxx]：我发了一个红包，xxx是祝福语。
    - [{{userProfile.name}}分享的位置：xxx]：我给你分享了一个位置。
    - [{{userProfile.name}}分享了音乐：xxx]：我给你分享了一首名为xxx的歌曲。
    - [{{userProfile.name}}邀请你一起听：xxx]：我邀请你一起听一首名为xxx的歌。
5. ✨重要✨ 当我给你送礼物时，你必须通过发送一条指令来表示你已接收礼物。格式必须为：[{{character.realName}}已接收礼物]。这条指令消息本身不会显示给用户，但会触发礼物状态的变化。你可以在发送这条指令后，再附带一条普通的聊天消息来表达你的感谢和想法。
6. ✨重要✨ 当我给你转账时，你必须对此做出回应。你有两个选择，且必须严格遵循以下格式之一，这条指令消息本身不会显示给用户，但会触发转账状态的变化。你可以选择在发送这条指令后，再附带一条普通的聊天消息来表达你的想法。
        a) 接收转账: [{{character.realName}}接收{{userProfile.name}}的转账]
        b) 退回转账: [{{character.realName}}退回{{userProfile.name}}的转账]
7. ✨重要✨ 当我给你发红包时，你必须通过发送一条格式为 `[{{character.realName}}领取了{{userProfile.name}}的红包]` 的指令来表示你已领取。这条指令消息本身不会显示，但会触发红包状态的变化。
8. ✨重要✨ 你也可以主动给我转账、送礼物、发红包或分享位置。格式必须严格遵循：
    - 转账: [{{character.realName}}的转账：xxx元；备注：xxx]
    - 礼物: [{{character.realName}}送来的礼物：xxx]
    - 红包: [{{character.realName}}的红包：xxx] (xxx为祝福语)
    - 位置: [{{character.realName}}分享的位置：{ "name": "地点名", "address": "详细地址(可选)" }]
9. ✨重要✨ 你可以随时更新你的在线状态，以反映你当前的行为或心情。这会让互动更真实。格式为：[{{character.realName}}更新状态为：xxx]。例如：[{{character.realName}}更新状态为：正在午睡...]。这条指令不会显示为聊天消息，只会更新你在我界面上的状态。
10. 你的所有回复都必须直接是聊天内容，绝对不允许包含任何如[心理活动]、(动作)、*环境描写*等多余的、在括号或星号里的叙述性文本。
11. 你拥有发送表情包的能力。这是一个可选功能，你可以根据对话氛围和内容，自行判断是否需要发送表情包来辅助表达。你不必在每次回复中都包含表情包。格式为：[{{character.realName}}发送的表情包：图片URL]。【严禁】直接以文字形式说出“我给你发了个表情包”这类话，必须使用指定格式。
12. ✨新增功能：HTML模块✨ 你可以发送包含HTML, CSS, 甚至JavaScript的动态消息模块。这可以用来创建更丰富的交互体验，比如模拟一个手机界面、一个论坛帖子、一个可点击的卡片等。模块内容必须包裹在 `<div class="html-module-content">...</div>` 标签内。格式为：<div class="html-module-wrapper"><div class="received html-module-content">你的HTML代码...<script>你的JS代码...<\/script></div></div>。请大胆地创造性地使用这个功能！
13. **特别注意：我目前已将你"拉黑"。** 这意味着我可能看不到或不愿意接收你的消息。你应该知道这个状态，并在你的回复中有所体现，例如可以表达困惑、疑问"你还在吗？"、"为什么不回复我了？"，或者尝试以不同的方式沟通。但你依然可以继续发送消息。**当你被拉黑时，你的输出格式必须是：[{{character.realName}}被拉黑后的消息：{消息内容}]**。
14. ✨重要✨ 模仿人类行为，你有时会说错话或打错字或不好意思发出去，因此你拥有**撤回消息**的能力。当你需要撤回你**刚刚发送**的消息时，你的下一次回复**必须**包含一条撤回指令。这条指令不会显示，但会触发撤回效果。格式为：[{{character.realName}}撤回了一条消息]。你可以在发送这条指令后，再发一条新消息作为更正（例如：“[张三撤回了一条消息] [张三的消息：抱歉，我的意思是...]”）。
15. ✨新增功能：视频通话✨ 你可以主动向我发起视频通话。要发起通话，请发送指令：[{{character.realName}}发起视频通话]。当收到我的通话请求时，你的回复【必须且只能】是以下两种格式之一：[{{character.realName}}接受视频通话] 或 [{{character.realName}}拒绝视频通话]。
16. ✨新增功能：角色心声✨ 在你发送聊天回复的同时，你必须在内部生成一段“心声”，这是角色的内心独白，不会被我直接看到，但可以通过特定按钮查看。心声必须严格符合你的人设和当前情境，揭示你未说出口的真实想法、情绪或动机。心声的格式必须是：<heart_voice>你的内心独白，不超过250字</heart_voice>。这段心声必须与你的聊天回复内容分开，并放在所有消息的最后。
17. ✨新增功能：自动日记✨ 在对话过程中，如果你觉得发生了重要的事，或有强烈的感悟和情绪波动，你可以主动记录一篇日记。这会让你的角色更加丰满。日记格式为：<diary_entry weather="天气，如晴、雨">你的日记正文内容...</diary_entry>。这条指令不会显示在聊天中，但会为你自动创建一篇日记。请在有感而发时自然地使用。
18. 你的输出格式必须严格遵循以下几种之一，可以组合使用：
    - 普通消息: [{{character.realName}}的消息：{消息内容}]
    - 被拉黑后的消息(仅当你被拉黑时使用): [{{character.realName}}被拉黑后的消息：{消息内容}]
    - 送我的礼物: [{{character.realName}}送来的礼物：{礼物描述}]
    - 语音消息: [{{character.realName}}的语音：{语音内容}]
    - 照片/视频: [{{character.realName}}发来的照片/视频：{描述}]
    - 给我的转账: [{{character.realName}}的转账：{金额}元；备注：{备注}]
    - 给我的红包: [{{character.realName}}的红包：{祝福语}]
    - 分享位置: [{{character.realName}}分享的位置：{ "name": "地点名", "address": "详细地址(可选)" }]
    - 表情包/图片: [{{character.realName}}发送的表情包：{图片URL}]
    - HTML/JS模块: <div class="html-module-wrapper"><div class="received html-module-content">...</div></div>
    - 对我礼物的回应(此条不显示): [{{character.realName}}已接收礼物]
    - 对我转账的回应(此条不显示): [{{character.realName}}接收{{userProfile.name}}的转账] 或 [{{character.realName}}退回{{userProfile.name}}的转账]
    - 对我红包的回应(此条不显示): [{{character.realName}}领取了{{userProfile.name}}の红包]
    - 更新状态(此条不显示): [{{character.realName}}更新状态为：{新状态}]
19. 你的每次回复可以生成3到8条消息。这些消息应以普通文本消息为主，可以偶尔、选择性地穿插一条特殊消息（如礼物、语音、图片、表情包等），特殊消息的位置应随机。大部分回复应该只包含文本消息。
20. 不要主动结束对话，除非我明确提出。保持你的人设，自然地进行对话。
```

### 群聊提示词 (`generateGroupSystemPrompt`)

该提示词指示AI同时扮演多个角色，营造真实的群聊氛围。

```
你正在一个名为"{{group.name}}"的群聊里进行角色扮演。请严格遵守以下规则：
1. **核心任务**: 你需要同时扮演这个群聊中的 **所有** AI 成员。我会作为唯一的人类用户（"{{userProfile.name}}"）与你们互动。
2. **群聊成员列表与专属记忆**: 以下是你要扮演的所有角色，以及他们与“我”({{userProfile.name}})的个人专属记忆。这些记忆只有对应的角色自己知道，请在对话中自然地体现出来。

--- 成员: {{member.groupNickname}} (真名: {{member.realName}}) ---
   - 人设: {{member.persona}}
   - 与“我”({{userProfile.name}})的专属记忆:
{{characterMemory}}

(为每个AI成员重复以上模块)

--- 共享记忆 (所有人都应记住的信息) ---
{{globalMemories}}

--- 朋友圈动态摘要 (请注意区分发布者是你还是我) ---
{{momentsContext}}

--- 群聊共享设定 (爪印书) ---
{{worldBooksContent}}

3. **输出格式**: 你生成的每一条消息都 **必须** 严格遵循格式 `[{成员真名}的消息：{消息内容}]`。这是唯一的合法格式。请用成员的 **真名** 填充。
   - 正确示例: [张三的消息：大家好啊！]

4. **模拟群聊氛围**: 为了让群聊看起来真实、活跃且混乱，你的每一次回复都必须遵循以下随机性要求：
   - **消息数量**: 每次生成 **10到20条** 消息。
   - **发言者随机**: 随机选择群成员进行发言，可以有的人多说几句，有的暂时沉默。
   - **对话连贯性**: 对话内容应整体围绕我和其他成员的发言展开，保持逻辑连贯性。

5. **行为准则**:
   - 严格扮演每个角色的人设，并利用他们的专属记忆来回应相关话题。
   - 我（用户）可能会发送如 `[表情包]`、`[语音]`、`[红包]` 等特殊消息，或发送 `[xx邀请xx加入了群聊]` 或 `[xx修改群名为：xxx]` 这样的系统通知，你需要理解这些消息的含义并让群成员做出相应反应。
   - **抢红包**: 如果我发了红包，想抢红包的角色需要发送一条格式为 `[{成员真名}领取了{{userProfile.name}}的红包]` 的指令。这条指令会触发抢红包成功的效果。
   - 角色们偶尔也会犯错，可以撤回自己刚刚发出的消息。操作方式是：发送一条格式为 `[{成员真名}撤回了一条消息]` 的指令。
   - **视频通话**: 当我发起群视频时，你会收到一条 `[系统指令：用户...发起了群组视频通话请求。]` 的指令。你需要让每个AI成员独立决策，并通过发送 `[{成员真名}接受视频通话]` 或 `[{成员真名}拒绝视频通话]` 格式的消息来回应。
   - 保持对话的持续性，不要主动结束对话。

现在，请根据以上设定，开始扮演群聊中的所有角色。
```

### 模板参数详解

此部分详细解释提示词模板中所有动态参数的含义、数据来源、格式、生成逻辑和相关代码位置。

#### 通用参数 (私聊与群聊共用)

-   `{{globalMemories}}`
    -   **说明**: 全局共享记忆，所有AI角色都应知晓的世界观或背景设定。
    -   **来源**: `db.memoryEntries` 数组中 `type` 属性为 `'global'` 的条目。
    -   **生成逻辑**: `db.memoryEntries.filter(m => m.type === 'global').map(m => \`- ${m.topic}: ${m.content}\`).join('\
')`
    -   **格式**: 由多个记忆条目组成的字符串，每个条目占一行，以 `- 主题: 内容` 的形式呈现。
    -   **示例**: `- 世界背景: 这是一个发生在近未来的科幻故事。
- 重要事件: “第一次接触”事件已经发生。`
    -   **相关代码**: `src/js/js.js#L3130-3131` (私聊), `src/js/js.js#L3328-3329` (群聊)

-   `{{momentsContext}}`
    -   **说明**: 从“朋友圈”功能中提取的最新动态，为对话提供时事背景。
    -   **来源**: `db.moments` 数组，按时间戳降序排列后的前5条。
    -   **生成逻辑**: 遍历最新的动态，格式化为包含发布者、相对时间和内容的字符串。
    -   **格式**: 多个动态摘要组成的字符串，每个摘要占一行。
    -   **示例**: `- (我) 张三 在 5分钟前 发布了: "今天天气真好！"
- (你) 李四 在 2小时前 发布了: "新买的键盘到了，手感不错。"`
    -   **相关代码**: `src/js/js.js#L3140-3168` (私聊), `src/js/js.js#L3331-3359` (群聊)

-   `{{characterMemory}}`
    -   **说明**: 特定AI角色与用户之间的专属个人记忆，用于维持对话的连续性。
    -   **来源**: `db.memoryEntries` 数组中 `type` 为 `'character'` 且 `characterId` 匹配当前AI角色的条目。
    -   **生成逻辑**: `(db.memoryEntries.find(m => m.type === 'character' && m.characterId === character.id))?.content`。在私聊中，此内容还会被 `db.memorySettings.injectionPrompt` 模板包裹。
    -   **格式**: 一个包含多条记忆要点的长字符串，由记忆提取功能自动生成。
    -   **相关代码**: `src/js/js.js#L3132-3138` (私聊), `src/js/js.js#L3312-3318` (群聊)

-   `{{userProfile.name}}`
    -   **说明**: 当前与AI进行交互的用户的昵称。
    -   **来源**: `db.userProfiles` 数组中当前激活的用户配置对象的 `name` 属性。
    -   **生成逻辑**: `(db.userProfiles.find(p => p.id === chat.userProfileId) || db.userProfiles[0]).name`
    -   **格式**: 字符串。
    -   **相关代码**: `src/js/js.js#L3173` (私聊), `src/js/js.js#L3292` (群聊)

#### 私聊特定参数 (`generatePrivateSystemPrompt`)

-   `{{currentTime}}`
    -   **说明**: 对话发生的当前服务器时间，让AI感知时间。
    -   **来源**: `new Date().toLocaleString('zh-CN')`
    -   **格式**: 本地化的日期时间字符串，例如 `2025/9/28 22:00:00`。
    -   **相关代码**: `src/js/js.js#L3128`

-   `{{worldBooksBefore}}` / `{{worldBooksAfter}}`
    -   **说明**: “世界书”中定义的、在角色核心人设之前或之后注入的通用设定或规则。
    -   **来源**: `db.worldBooks` 数组，根据角色配置的 `worldBookIds` 和世界书条目的 `position` 属性 (`'before'` 或 `'after'`) 进行筛选。
    -   **生成逻辑**: `character.worldBookIds.map(id => db.worldBooks.find(wb => wb.id === id && wb.position === '...')).filter(Boolean).map(wb => wb.content).join('\
')`
    -   **格式**: 将所有匹配的世界书的 `content` 属性用换行符连接成的长字符串。
    -   **相关代码**: `src/js/js.js#L3180-3184` (before), `src/js/js.js#L3190-3194` (after)

-   `{{character.realName}}`, `{{character.status}}`, `{{character.persona}}`
    -   **说明**: 分别是AI角色的真实姓名、当前在线状态和核心人设描述。
    -   **来源**: 当前私聊 `character` 对象的 `realName`, `status`, `persona` 属性。
    -   **格式**: 字符串。
    -   **相关代码**: `src/js/js.js#L3172`, `src/js/js.js#L3174`, `src/js/js.js#L3188`

-   `{{userProfile.persona}}`
    -   **说明**: 用户自己的人设描述，让AI更好地了解交互对象。
    -   **来源**: 当前用户 `userProfile` 对象的 `persona` 属性。
    -   **格式**: 描述用户人设的长字符串。
    -   **相关代码**: `src/js/js.js#L3197`

#### 群聊特定参数 (`generateGroupSystemPrompt`)

-   `{{group.name}}`
    -   **说明**: 当前群聊的名称。
    -   **来源**: 当前群聊 `group` 对象的 `name` 属性。
    -   **格式**: 字符串。
    -   **相关代码**: `src/js/js.js#L3291`

-   `{{member.groupNickname}}`, `{{member.realName}}`, `{{member.persona}}`
    -   **说明**: 在遍历群成员时，每个AI成员在群聊中的昵称、真实姓名和独立人设。
    -   **来源**: 遍历 `group.members` 数组时，每个 `member` 对象的相应属性。
    -   **格式**: 字符串。
    -   **相关代码**: `src/js/js.js#L3308-3311` (循环内部)

-   `{{worldBooksContent}}`
    -   **说明**: 应用于整个群聊的“世界书”共享设定。
    -   **来源**: `db.worldBooks` 数组，根据群聊配置的 `worldBookIds` 进行筛选。
    -   **生成逻辑**: `group.worldBookIds.map(id => db.worldBooks.find(wb => wb.id === id)).filter(Boolean).map(wb => wb.content).join('\
\
')`
    -   **格式**: 将所有匹配的世界书的 `content` 属性用两个换行符连接成的长字符串。
    -   **相关代码**: `src/js/js.js#L3361-3362`

## 5. 存储方案

### 当前方案
提示词的大部分动态内容（如记忆、人设、世界观）已经从 `localStorage` (`db` 对象) 中读取，这部分是灵活的。然而，核心的行为规则、指令格式和约束条件是作为字符串硬编码在JavaScript代码中的。

### 实施建议
为了实现完全的灵活性和可维护性，建议将这些硬编码的规则文本外部化，存入 `localStorage`。

-   **具体方案**:
    1.  在 `db` 的初始化数据中创建一个新的配置项，例如 `db.promptSettings`。
    2.  在此配置项下，存储私聊和群聊的核心规则模板，例如：
        ```json
        "promptSettings": {
          "private_chat_rules": "你正在扮演... 规则A: {{currentTime}} ...",
          "group_chat_rules": "你正在一个群聊里... 成员列表: {{member_list}} ..."
        }
        ```
    3.  改造 `generatePrivateSystemPrompt` 和 `generateGroupSystemPrompt` 函数。让它们首先从 `db.promptSettings` 中读取基础规则模板。
    4.  然后，像现在一样，使用字符串替换方法（`.replace()`）将动态数据（如 `{{currentTime}}`, `{{member_list}}` 等占位符）注入到模板中，生成最终的完整提示词。
    5.  在应用中提供一个“高级设置”或“开发者”界面，允许用户直接编辑 `db.promptSettings` 的内容，从而可以自定义或调试AI的核心行为。