@import url('https://fonts.googleapis.com/css2?family=Varela+Round&display=swap');

:root {
    --primary-color: #FFD97D; 
    --primary-color-rgb: 255, 217, 125;
    --secondary-color: #B78460; 
    --accent-color: #A0D8EF;
    --accent-color-rgb: 160, 216, 239;
    --danger-color: #ef5350;
    --danger-color-rgb: 239, 83, 80;
    --neutral-color: #bdbdbd;
    --neutral-color-rgb: 189, 189, 189;
    --bg-color: rgba(255, 217, 125, 0.08); 
    --top-pinned-bg: rgba(255, 217, 125, 0.15); 
    --text-color: #5D4037; 
    --white-color: #FDFBF6; 
    --border-radius: 22px; 
    --phone-corner-radius: 0px; 
    --font-family: 'Varela Round', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, 'Helvetica Neue', Arial, sans-serif; 
    --online-status-color: #4CAF50; 
    --moments-bg: #FFFFFF; 
    --moments-interactions-bg: #F7F7F7;
}
*, *::before, *::after { box-sizing: border-box; }
body { font-family: var(--font-family); margin: 0; padding: 0; background: linear-gradient(135deg, #FFEBCD, #D2B48C); display: flex; align-items: center; justify-content: center; min-height: 100vh; }
.phone-screen { width: 100%; max-width: 420px; height: calc(var(--vh, 1vh) * 100); max-height: 850px; background-color: var(--white-color); box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1); border-radius: var(--phone-corner-radius); overflow: hidden; position: relative; display: flex; flex-direction: column; }

        /* --- 核心修复：屏幕布局 --- */
.screen {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    position: absolute; /* 使用绝对定位，防止互相影响 */
    top: 0;
    left: 0;
    opacity: 0; /* 默认透明 */
    visibility: hidden; /* 默认不可见 */
    transform: scale(0.95); /* 初始缩放状态 */
    transition: opacity 0.4s ease, visibility 0.4s, transform 0.4s ease; /* 过渡动画 */
    z-index: 1; /* 默认在底层 */
}
.screen.active {
    opacity: 1; /* 激活时完全可见 */
    visibility: visible;
    transform: scale(1); /* 恢复正常大小 */
    z-index: 2; /* 提到顶层显示 */
}

.app-header { display: flex; justify-content: space-between; align-items: center; padding: 10px 20px; background-color: rgba(253, 251, 246, 0.8); backdrop-filter: blur(10px); border-bottom: 1px solid #eee; flex-shrink: 0; position: relative; z-index: 10; }
.app-header .back-btn, .app-header .action-btn { background: none; border: none; font-size: 24px; font-weight: bold; color: var(--secondary-color); cursor: pointer; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; transition: transform 0.2s ease; }
.app-header .back-btn:active, .app-header .action-btn:active { transform: scale(0.9); }
#cancel-multi-select-btn { font-size: 16px !important; font-weight: 500 !important; color: var(--text-color) !important; width: auto !important; height: auto !important; }
.app-header .action-btn-group { display: flex; align-items: center; gap: 5px; }
#chat-room-header-select .action-btn-group { gap: 0; } 
.app-header .action-btn-group .action-btn { font-size: 16px; font-weight: 600; width: auto; padding: 6px 10px; border-radius: 10px; color: var(--text-color); }
.app-header .action-btn-group .action-btn:disabled { color: #ccc; cursor: not-allowed; }
.app-header .action-btn-group #create-group-btn {
    background-color: rgba(var(--primary-color-rgb), 0.7);
    color: var(--text-color);
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}
.app-header .action-btn-group #add-item-btn { font-size: 28px; padding: 0; width: 40px; height: 40px; background-color: transparent; color: var(--primary-color); border-radius: 50%; }
.app-header .action-btn svg { width: 26px; height: 26px; }
.app-header .action-btn img { width: 28px; height: 28px; }
.app-header .title-container { display: flex; flex-direction: column; align-items: center; text-align: center; position: absolute; left: 50%; transform: translateX(-50%); }
.app-header .title { font-size: 18px; font-weight: 600; color: var(--text-color); margin: 0; }
.app-header .subtitle { font-size: 12px; color: #888; display: flex; align-items: center; margin-top: 2px; }
.online-indicator { width: 8px; height: 8px; border-radius: 50%; background-color: var(--online-status-color); margin-right: 5px; }
.app-header .placeholder { width: 40px; }
#home-screen { justify-content: space-between; background-size: cover; background-position: center; transition: background-image 0.5s ease-in-out; padding: 50px 0; }
.time-widget { text-align: center; padding: 0 20px; color: var(--text-color); }
.time-widget .time { font-size: 72px; font-weight: 600; }
.time-widget .date { font-size: 18px; color: #666; }
#home-screen.day-mode .time-widget, #home-screen.day-mode .time-widget .date, #home-screen.day-mode .app-icon .app-name { color: var(--white-color); text-shadow: 0 1px 3px rgba(0,0,0,0.4); }
.home-layout { display: flex; padding: 20px 40px; gap: 20px; align-items: center; justify-content: center; margin-top: 20px; flex-grow: 1; }
.home-layout-left { flex-shrink: 0; margin-right: 10px; }
.home-layout-right { flex-grow: 1; display: grid; grid-template-columns: repeat(2, 1fr); gap: 25px; }
.app-icon-large .icon-img { width: 130px; height: 130px; border-radius: 34px; }
.app-icon-large .app-name { font-size: 14px; }
.dock { display: flex; justify-content: center; align-items: center; padding: 10px; background-color: rgba(253, 251, 246, 0.75); backdrop-filter: blur(10px); border-radius: 28px; margin: 0 20px; min-height: 80px; gap: 30px; }
.app-icon { display: flex; flex-direction: column; align-items: center; cursor: pointer; text-decoration: none; }
.icon-img { width: 60px; height: 60px; border-radius: 18px; margin-bottom: 8px; box-shadow: 0 8px 25px rgba(183, 132, 96, 0.15); transition: transform 0.2s cubic-bezier(0.34, 1.56, 0.64, 1), box-shadow 0.2s ease; object-fit: cover; border: 3px solid rgba(255, 255, 255, 0.5); }
.app-icon:hover .icon-img { transform: translateY(-6px) scale(1.1); box-shadow: 0 10px 20px rgba(183, 132, 96, 0.2); }
.app-icon:active .icon-img { transform: translateY(-2px) scale(0.95); }
.app-icon .app-name { font-size: 12px; color: var(--text-color); font-weight: 500; }
.content { flex-grow: 1; overflow-y: auto; padding: 20px; position: relative; background-image: url('https://www.transparenttextures.com/patterns/paws.png'); background-color: rgba(253, 251, 246, 0.5); }
#chat-list-screen .content, #contacts-screen-panel .content, #me-screen-panel .content { padding: 0; }
.placeholder-text { text-align: center; color: #aaa; margin-top: 50px; }
.modal-overlay { position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.4); z-index: 100; display: none; align-items: center; justify-content: center; animation: fadeInModal 0.3s ease; }
@keyframes fadeInModal { from { opacity: 0; } to { opacity: 1; } }
.modal-overlay.visible { display: flex; }
.modal-window { background: var(--white-color); padding: 25px; border-radius: var(--border-radius); box-shadow: 0 5px 25px rgba(0,0,0,0.15); width: 85%; max-width: 340px; animation: slideUp 0.4s ease-out; }
.modal-window h3 { margin-top: 0; text-align: center; font-family: 'Comic Sans MS', 'Chalkduster', 'Handwriting', cursive; color: var(--secondary-color); }
@keyframes slideUp { from { transform: translateY(20px); opacity: 0; } to { transform: translateY(0); opacity: 1; } }
#edit-group-member-modal, #create-member-for-group-modal { z-index: 102; }
#edit-contact-modal, #edit-profile-modal { z-index: 102; }
#heart-voice-modal .modal-window { line-height: 1.6; color: var(--text-color); }
#edit-group-member-modal .avatar-preview, #create-member-for-group-modal .avatar-preview, #add-char-modal .avatar-preview, #edit-contact-modal .avatar-preview, #create-profile-modal .avatar-preview, #edit-profile-modal .avatar-preview { width: 80px; height: 80px; border-radius: 50%; }
.context-menu {
    position: fixed;
    z-index: 1000;
    background: rgba(50, 50, 50, 0.85);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 18px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.2);
    overflow: hidden;
    display: flex;
    padding: 6px;
    animation: fadeInModal 0.1s ease;
}
.context-menu-item {
    padding: 9px 16px;
    cursor: pointer;
    color: white;
    font-size: 14px;
    white-space: nowrap;
    border-radius: 14px;
    transition: background-color 0.2s ease;
}
.context-menu-item:hover {
    background-color: rgba(255, 255, 255, 0.15);
}
.context-menu-item.danger {
    color: #FF8A80;
}
.context-menu-item.danger:hover {
    background-color: rgba(255, 138, 128, 0.2);
}
.action-sheet-overlay { position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.4); z-index: 200; display: none; align-items: flex-end; animation: fadeInModal 0.3s ease; }
.action-sheet-overlay.visible { display: flex; }
.action-sheet { background: #f7f7f7; width: 100%; border-top-left-radius: 20px; border-top-right-radius: 20px; padding: 10px; padding-bottom: calc(10px + env(safe-area-inset-bottom)); animation: slideUp 0.3s ease-out; }
.action-sheet-button { width: 100%; background: white; border: none; padding: 15px; font-size: 16px; color: var(--secondary-color); font-weight: 500; cursor: pointer; border-radius: 10px; margin-bottom: 8px; text-align: center; }
.action-sheet-button.danger { color: #e53935; }
.action-sheet-button:last-child { margin-bottom: 0; }
.action-sheet label.action-sheet-button { text-align: center; display: block; }
#world-book-screen .content, #memory-core-screen .content { padding: 10px 0 0 0; }
.list-container { list-style: none; margin: 0; padding: 0; }
.list-header {
    padding: 4px 20px;
    font-size: 14px;
    font-weight: 600;
    color: var(--secondary-color);
    background-color: #f5f5f5;
}
.list-item { display: flex; align-items: center; padding: 10px 20px; cursor: pointer; border-bottom: 1px solid #f0f0f0; transition: background-color 0.2s ease, border-left 0.2s ease, padding-left 0.2s ease; position: relative; border-left: 4px solid transparent; }
.list-item:hover { background-color: var(--bg-color); border-left: 4px solid var(--primary-color); padding-left: 16px !important; }
.chat-item.pinned { background-color: var(--top-pinned-bg); }
.avatar-wrapper { position: relative; flex-shrink: 0; }
.unread-badge { position: absolute; top: 0; right: 12px; width: 12px; height: 12px; background-color: red; border-radius: 50%; border: 2px solid white; z-index: 2; }
.chat-avatar { width: 50px; height: 50px; border-radius: 50%; margin-right: 15px; object-fit: cover; flex-shrink: 0; background-color: #eee; }
.group-avatar { border-radius: 15px; }
.item-details { flex-grow: 1; overflow: hidden; }
.item-details-row { display: flex; justify-content: space-between; align-items: center; }
.item-name { font-weight: 600; color: var(--text-color); font-size: 16px; }
.item-preview-wrapper { display: flex; align-items: center; gap: 8px; margin-top: 4px; }
.item-preview { font-size: 14px; color: #888; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; flex-grow: 1; }
.pin-badge { background-color: var(--primary-color); color: var(--text-color); font-size: 10px; font-weight: bold; padding: 2px 6px; border-radius: 5px; white-space: nowrap; flex-shrink: 0; }
#chat-room-screen { background-size: cover; background-position: center; }
#chat-room-screen .content { display: flex; flex-direction: column; padding: 10px; padding-bottom: 10px; transition: padding-bottom 0.3s ease; }
.message-area { 
    flex-grow: 1; 
    overflow-y: auto; 
    padding: 0 10px; 
    scroll-behavior: smooth;
}
.message-wrapper { 
    display: flex; 
    margin-bottom: 12px; 
    align-items: flex-start; 
    transition: background-color 0.2s; 
    flex-direction: column;
    position: relative;
}
.html-module-wrapper {
    display: flex;
    margin-bottom: 12px;
    width: 100%;
}
.html-module-wrapper.sent {
        flex-direction: row-reverse;
}
.html-module-content {
    margin: 0 8px;
    width: fit-content;
    max-width: calc(100% - 16px);
    position: relative;
}
.message-wrapper.sent { align-items: flex-end; }
.message-wrapper.received { align-items: flex-start; }
.message-wrapper.group-message { margin-bottom: 18px; }
.message-wrapper.system-notification { align-items: center; }
.message-bubble-row { display: flex; width: 100%; align-items: flex-start; }
.message-wrapper.sent .message-bubble-row { flex-direction: row-reverse; }
.message-wrapper.multi-select-selected::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -4px;
    right: -4px;
    bottom: -2px;
    background-color: rgba(144, 202, 249, 0.25);
    border: 2px solid #90caf9;
    border-radius: 12px;
    pointer-events: none;
}
.message-info { display: flex; flex-direction: column; align-items: center; position: relative; flex-shrink: 0; }
.group-nickname { position: absolute; top: -15px; font-size: 11px; color: #888; white-space: nowrap; width: 70px; text-align: center; overflow: hidden; text-overflow: ellipsis; }
.message-avatar { width: 36px; height: 36px; border-radius: 50%; object-fit: cover; cursor: pointer; }
.message-time { font-size: 9px; color: #aaa; margin-top: 3px; }
.message-bubble { max-width: 220px; margin: 0 8px; cursor: pointer; }
.message-bubble .content { 
    padding: 10px 14px; 
    border-radius: 8px; 
    line-height: 1.5; 
    box-shadow: 0 1px 2px rgba(0,0,0,0.08);
    font-family: var(--font-family);
    position: relative;
    word-wrap: break-word;
    overflow-wrap: break-word;
    overflow-x: hidden;
}
.message-bubble.is-text-bubble .content {
        backdrop-filter: blur(5px); 
    -webkit-backdrop-filter: blur(5px); 
}

.message-bubble.user.is-text-bubble .content {
    word-break: break-all;
}

.message-bubble.user.is-text-bubble .content::before, .message-bubble.ai.is-text-bubble .content::before {
    content: '';
    position: absolute;
    top: 10px;
    width: 8px;
    height: 8px;
    background-color: inherit;
}
.message-bubble.user.is-text-bubble .content::before {
    right: -4px;
    clip-path: polygon(0 50%, 100% 0, 100% 100%);
}
.message-bubble.ai.is-text-bubble .content::before {
    left: -4px;
    clip-path: polygon(0 0, 100% 50%, 0 100%);
}

.message-bubble .reply-quote-container {
    background-color: rgba(0,0,0,0.05);
    padding: 6px 10px;
    margin: -2px 0 8px;
    border-left: 3px solid var(--secondary-color);
    border-radius: 4px;
    font-size: 0.9em;
    opacity: 0.8;
}
.reply-quote-container .author {
    font-weight: bold;
    font-size: 0.9em;
}
.reply-quote-container .text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.blocked-indicator { width: 16px; height: 16px; background-color: #ef5350; color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: bold; flex-shrink: 0; margin: auto 6px; }
.message-wrapper.sent .blocked-indicator { order: -1; }
.message-wrapper.received .message-bubble-row { align-items: center; }
.system-notification-bubble { background-color: rgba(200, 200, 200, 0.5); color: #666; font-size: 12px; padding: 4px 10px; border-radius: 10px; text-align: center; cursor: pointer; }
.image-bubble { max-width: 120px; border-radius: 8px; margin: 0 8px; padding: 4px; background-color: rgba(255, 255, 255, 0.5); backdrop-filter: blur(5px); -webkit-backdrop-filter: blur(5px); box-shadow: 0 2px 4px rgba(0,0,0,0.1); cursor: pointer; }
.image-bubble img { width: 100%; height: auto; display: block; border-radius: 4px; }

.voice-message-card {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin: 0 8px;
}
.voice-bubble { display: flex; align-items: center; justify-content: space-between; padding: 8px 12px; border-radius: 8px; backdrop-filter: blur(5px); -webkit-backdrop-filter: blur(5px); cursor: pointer; transition: all 0.2s ease; min-width: 90px; max-width: 200px; }
.message-wrapper.sent .voice-bubble { flex-direction: row-reverse; }
.voice-bubble .play-icon { width: 18px; height: 18px; flex-shrink: 0; }
.voice-bubble .duration { font-size: 13px; margin: 0 8px; white-space: nowrap; }
.message-wrapper.sent .play-icon { transform: scaleX(-1); }
.voice-transcript {
    padding: 8px 12px;
    border-radius: 10px;
    line-height: 1.6;
    max-width: 220px;
    display: none;
    animation: fadeInModal 0.3s ease;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    position: relative;
    transform: rotate(-1deg);
}
.voice-transcript.active { display: block; }
.voice-transcript::before {
    content: '';
    position: absolute;
    top: -5px;
    width: 10px;
    height: 10px;
    background: inherit;
    transform: rotate(45deg);
}
.message-wrapper.sent .voice-transcript::before { right: 15px; }
.message-wrapper.received .voice-transcript::before { left: 15px; }

.pv-card { width: 230px; aspect-ratio: 1 / 1; background-color: #f0f0f0; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); overflow: hidden; position: relative; cursor: pointer; margin: 0 8px; }
.pv-card-image-overlay { position: absolute; top: 0; left: 0; width: 100%; height: 100%; background-size: cover; background-position: center; transition: opacity 0.5s ease-in-out; z-index: 2; }
.pv-card-image-overlay.hidden { opacity: 0; pointer-events: none; }
.pv-card-content { padding: 15px; height: 100%; overflow-y: auto; color: var(--text-color); line-height: 1.6; font-size: 15px; background-color: white; position: relative; z-index: 1; }
.pv-card-footer { background: linear-gradient(to top, rgba(0,0,0,0.6), transparent); color: white; padding: 20px 10px 8px; font-size: 12px; display: flex; align-items: center; gap: 5px; position: absolute; bottom: 0; left: 0; width: 100%; z-index: 3; pointer-events: none; transition: opacity 0.5s ease-in-out; }
.pv-card-footer.hidden { opacity: 0; }
.pv-card-footer svg { width: 14px; height: 14px; fill: white; flex-shrink: 0; }

.transfer-card {
    width: 230px;
    border-radius: 12px;
    margin: 0 8px;
    overflow: hidden;
    position: relative;
    box-shadow: 0 4px 12px rgba(183, 132, 96, 0.2);
    color: #5D4037;
    background-color: #FFF9E6;
    cursor: pointer;
}
.transfer-card::before {
    content: '';
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    background-image: url('https://tc.z.wiki/autoupload/f/OzKndMK49LVR62VsqvAE3XljXEeVtjg7lTJFugegT_eyl5f0KlZfm6UsKj-HyTuv/20250802/iK45/1080X1048/IMG_20250802_025159.jpg');
    background-size: cover;
    background-position: center;
    filter: blur(3px);
    opacity: 0.5;
    z-index: 1;
}
.transfer-content { padding: 15px; position: relative; z-index: 2; }
.transfer-header { display: flex; align-items: center; gap: 10px; }
.transfer-title { font-size: 16px; font-weight: 600; }
.transfer-amount { font-size: 32px; font-weight: bold; margin: 10px 0; }
.transfer-remark { font-size: 14px; color: #8D6E63; }
.transfer-footer {
    font-size: 12px;
    margin-top: 10px;
    padding-top: 8px;
    border-top: 1px solid rgba(93, 64, 55, 0.1);
    color: #A1887F;
}
.transfer-card.returned { background-color: #E0E0E0; cursor: default; }
.transfer-card.returned::before { display: none; }
.transfer-card.returned .transfer-title, .transfer-card.returned .transfer-amount { color: #9E9E9E; text-decoration: line-through; }
.transfer-card.received { cursor: default; }

.red-packet-card {
    width: 220px;
    background: linear-gradient(165deg, #FBC02D, #F9A825);
    border-radius: 10px;
    margin: 0 8px;
    color: #8D6E63;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(251, 192, 45, 0.4);
    position: relative;
    overflow: hidden;
}
.red-packet-card::before {
    content: '';
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    background-image: url('https://tc.z.wiki/autoupload/f/OzKndMK49LVR62VsqvAE3XljXEeVtjg7lTJFugegT_eyl5f0KlZfm6UsKj-HyTuv/20250802/eWoe/1199X1175/IMG_20250802_025209.jpg');
    background-size: cover;
    background-position: center;
    filter: blur(2px);
    opacity: 0.3;
    z-index: 1;
}
.red-packet-content { padding: 15px; position: relative; z-index: 2; }
.red-packet-header { display: flex; align-items: center; gap: 10px; }
.red-packet-open-icon { width: 24px; height: 24px; border: 2px solid #FFFFFF; color: #FFFFFF; background-color: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 14px; }
.red-packet-header .title { font-weight: 500; font-size: 16px; color: white; text-shadow: 0 1px 2px rgba(0,0,0,0.2); }
.red-packet-remark { font-size: 14px; margin-top: 8px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; color: white; }
.red-packet-footer {
    font-size: 12px;
    padding: 5px 15px;
    background-color: #FFFDE7;
    color: #AF821B;
    position: relative;
    z-index: 2;
}
.red-packet-card.claimed { background: linear-gradient(165deg, #E0E0E0, #BDBDBD); box-shadow: 0 2px 8px rgba(0,0,0,0.1); cursor: default; }
.red-packet-card.claimed .red-packet-header .title, .red-packet-card.claimed .red-packet-remark { color: #757575; text-shadow: none; }
.red-packet-card.claimed .red-packet-open-icon { border-color: #757575; color: #757575; background: none; }
.red-packet-card.claimed .red-packet-footer { background-color: #F5F5F5; color: #9E9E9E; }
.red-packet-card.claimed::before { opacity: 0.1; }

.location-card {
    width: 240px;
    border-radius: 10px;
    overflow: hidden;
    background: white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    cursor: pointer;
    margin: 0 8px;
}
.location-map {
    height: 120px;
    background-image: url('https://i.postimg.cc/13LKVN6z/IMG-20250802-025234.jpg');
    background-size: cover;
    background-position: center;
    position: relative;
    border-bottom: 1px solid #eee;
}
.location-info {
    padding: 10px;
}
.location-name {
    font-weight: 600;
    color: var(--text-color);
    font-size: 15px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.location-address {
    font-size: 12px;
    color: #aaa;
    margin-top: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.gift-card-flipper {
    width: 230px;
    height: 100px;
    perspective: 1000px;
    margin: 0 8px;
    cursor: pointer;
}
.gift-card {
    position: relative;
    width: 100%;
    height: 100%;
    transition: transform 0.6s;
    transform-style: preserve-3d;
}
.gift-card-flipper.flipped .gift-card {
    transform: rotateY(180deg);
}
.gift-card-front, .gift-card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    border-radius: 8px;
    box-shadow: 4px 4px 0px #ddd;
    border: 2px solid #333;
    overflow: hidden;
}

.gift-card-front {
    background-color: #fff;
    display: flex;
    align-items: center;
    padding: 10px;
}
.gift-card-back {
    background-color: rgba(240, 240, 240, 0.95);
    transform: rotateY(180deg);
    padding: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    line-height: 1.6;
    font-size: 14px;
    color: #555;
}
.gift-card-icon { width: 50px; height: 50px; margin-right: 15px; flex-shrink: 0; }
.gift-card-text { font-size: 16px; font-weight: bold; color: #333; font-family: 'Comic Sans MS', 'Chalkduster', 'Handwriting', cursive; }
.gift-card-received-stamp { position: absolute; top: 5px; right: 5px; font-size: 14px; font-weight: bold; color: var(--primary-color); border: 2px solid var(--primary-color); border-radius: 8px; padding: 2px 6px; transform: rotate(15deg) scale(1); opacity: 0; transition: opacity 0.3s, transform 0.3s; font-family: 'Comic Sans MS', 'Chalkduster', 'Handwriting', cursive; }
.gift-card.received .gift-card-received-stamp { opacity: 1; transform: rotate(15deg) scale(1); }

.file-card {
    width: 230px;
    background: #f9f9f9;
    border-radius: 12px;
    margin: 0 8px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.08);
    display: flex;
    align-items: center;
    gap: 15px;
    cursor: pointer;
}
.file-icon {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
}
.file-details {
    overflow: hidden;
}
.file-name {
    font-weight: 600;
    color: var(--text-color);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.file-size {
    font-size: 12px;
    color: #aaa;
    margin-top: 4px;
}
.forwarded-card {
    width: 230px;
    background: #fff;
    border: 1px solid #eee;
    border-radius: 12px;
    margin: 0 8px;
    padding: 10px 15px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    cursor: pointer;
}
.forwarded-header {
    font-size: 15px;
    font-weight: 600;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 8px;
}
.forwarded-preview {
    font-size: 13px;
    color: #888;
    line-height: 1.5;
    max-height: 60px;
    overflow: hidden;
    -webkit-mask-image: linear-gradient(to bottom, black 50%, transparent 100%);
    mask-image: linear-gradient(to bottom, black 50%, transparent 100%);
}

.load-more-btn { background-color: #e0e0e0; color: #757575; border: none; padding: 8px 16px; margin: 10px auto; border-radius: 15px; cursor: pointer; display: block; font-size: 13px; font-weight: 500; }
.load-more-btn:hover { background-color: #d1d1d1; }
.typing-indicator { text-align: center; color: #aaa; font-style: italic; font-size: 14px; padding: 10px 0; display: none; }
.typing-indicator.active .dot { animation: typing-blink 1.4s infinite both; display: inline-block; }
.typing-indicator.active .dot:nth-child(2) { animation-delay: .2s; }
.typing-indicator.active .dot:nth-child(3) { animation-delay: .4s; }
@keyframes typing-blink { 0% { opacity: .2; } 20% { opacity: 1; } 100% { opacity: .2; } }
#sticker-modal { position: absolute; bottom: 0; left: 0; right: 0; height: 0; background: #f7f7f7; border-top-left-radius: 20px; border-top-right-radius: 20px; box-shadow: 0 -5px 15px rgba(0,0,0,0.1); z-index: 25; display: flex; flex-direction: column; transition: height 0.3s ease-in-out; overflow: hidden;}
#sticker-modal.visible { height: 35%; max-height: 250px; }
#sticker-modal .header { padding: 10px 15px; font-weight: bold; color: var(--text-color); border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center; flex-shrink: 0;}
#sticker-modal .header div { display: flex; gap: 8px; }
.sticker-grid { flex-grow: 1; overflow-y: auto; padding: 15px; display: grid; grid-template-columns: repeat(auto-fill, minmax(70px, 1fr)); gap: 15px; }
.sticker-item { position: relative; display: flex; flex-direction: column; align-items: center; cursor: pointer; }
.sticker-item img { width: 60px; height: 60px; object-fit: contain; }
.sticker-item span { font-size: 12px; color: #666; margin-top: 5px; text-align: center; }
#add-sticker-modal .modal-window { max-width: 360px; }
#sticker-preview { width: 100px; height: 100px; border: 2px dashed #ddd; border-radius: 10px; margin: 0 auto 15px; display: flex; align-items: center; justify-content: center; color: #aaa; background-color: #f9f9f9; }
#sticker-preview img { max-width: 100%; max-height: 100%; }
.chat-input-wrapper { flex-shrink: 0; position: relative; background-color: #f7f7f7;}
.message-input-area {
    display: flex;
    align-items: center;
    padding: 8px;
    padding-bottom: calc(8px + env(safe-area-inset-bottom));
    border-top: 1px solid #eee;
    background: #f7f7f7;
    flex-shrink: 0;
    gap: 8px;
}
.message-input-area input { flex-grow: 1; border: none; padding: 12px; border-radius: 18px; background-color: #fff; }
.message-input-area input:focus { outline: none; box-shadow: 0 0 0 2px var(--primary-color); }
.message-input-area .icon-btn { background: none; border: none; color: #555; border-radius: 50%; width: 40px; height: 40px; cursor: pointer; flex-shrink: 0; display: flex; align-items: center; justify-content: center; transition: background-color 0.2s; }
.message-input-area .icon-btn:disabled { color: #ccc; cursor: not-allowed; }
.message-input-area .icon-btn svg { width: 24px; height: 24px; fill: currentColor; }
.message-input-area .send-btn {
    background: var(--primary-color);
    color: white;
    width: 36px;
    height: 36px;
    margin-left: -5px;
}
.message-input-area .send-btn svg {
    width: 20px;
    height: 20px;
}
.chat-plus-menu {
    height: 0;
    background-color: #f7f7f7;
    overflow: hidden;
    transition: height 0.3s ease-in-out;
    padding: 0 20px;
}
.chat-plus-menu.visible {
    height: 250px; /* Increased height for pagination */
    padding: 15px 20px calc(15px + env(safe-area-inset-bottom));
}
.plus-menu-slider-wrapper {
    width: 100%;
    overflow: hidden;
}
.plus-menu-slider {
    display: flex;
    width: 200%; /* Two pages */
    transition: transform 0.3s ease-in-out;
}
.plus-menu-page {
    width: 50%;
    flex-shrink: 0;
}
.plus-menu-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
}
.plus-menu-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
}
.plus-menu-item .icon-background {
    width: 60px;
    height: 60px;
    background-color: white;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}
.plus-menu-item svg {
    width: 32px;
    height: 32px;
    fill: #444;
}
.plus-menu-item span {
    font-size: 12px;
    color: #666;
}
.plus-menu-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    margin-top: 15px;
}
.plus-menu-pagination .dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #ccc;
    transition: background-color 0.3s;
    cursor: pointer;
}
.plus-menu-pagination .dot.active {
    background-color: var(--secondary-color);
}
#multi-select-bar { position: absolute; bottom: 0; left: 0; width: 100%; background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px); display: none; justify-content: space-between; align-items: center; padding: 10px 20px; padding-bottom: calc(10px + env(safe-area-inset-bottom)); border-top: 1px solid #eee; z-index: 20; border-bottom-left-radius: var(--phone-corner-radius); border-bottom-right-radius: var(--phone-corner-radius); animation: slideUp 0.3s ease-out; }
#multi-select-bar.visible { display: flex; }
.settings-sidebar { position: absolute; top: 0; right: -100%; width: 80%; height: 100%; background: #fff; box-shadow: -5px 0 15px rgba(0,0,0,0.1); transition: right 0.4s ease-in-out; z-index: 101; display: flex; flex-direction: column; }
.settings-sidebar.open { right: 0; }
.settings-sidebar .header { padding: 15px; border-bottom: 1px solid #eee; font-weight: bold; text-align: center; color: var(--secondary-color); }
.settings-sidebar .content { padding: 20px; overflow-y: auto; flex-grow: 1; }
.settings-sidebar .form-group textarea { height: 100px; resize: vertical; }
.settings-sidebar .avatar-setting { display: flex; flex-direction: column; align-items: center; gap: 15px; margin-bottom: 15px; }
.settings-sidebar .avatar-preview { width: 60px; height: 60px; border-radius: 50%; object-fit: cover; border: 2px solid var(--primary-color); cursor: pointer; }
.settings-sidebar .profile-selector { display: flex; align-items: center; background-color: #f9f9f9; border-radius: 12px; padding: 10px; margin-bottom: 10px; }
.settings-sidebar .profile-selector .avatar-container { flex-shrink: 0; cursor: pointer; }
.settings-sidebar .profile-selector img { width: 40px; height: 40px; border-radius: 50%; margin-right: 10px; }
.settings-sidebar .profile-selector .details { flex-grow: 1; }
.settings-sidebar .profile-selector .details p { margin: 0; font-weight: 600; color: var(--text-color); }
.settings-sidebar .profile-selector .details span { font-size: 12px; color: #888; }
.settings-sidebar .profile-selector .change-btn { background-color: var(--secondary-color); color: white; border: none; padding: 6px 10px; border-radius: 8px; font-size: 12px; flex-shrink: 0; margin-left: 10px; cursor: pointer; }
.form-group { margin-bottom: 20px; }
.form-group label { display: block; margin-bottom: 8px; color: var(--secondary-color); font-weight: 600; }
.form-group input, .form-group select, .form-group textarea { width: 100%; padding: 12px; border: 2px solid var(--secondary-color); border-radius: 10px; background-color: #fff; transition: border-color: 0.3s; font-family: var(--font-family); font-size: 14px; }
.form-group input:focus, .form-group select:focus, .form-group textarea:focus { outline: none; border-color: var(--primary-color); }
.form-group.radio-group { display: flex; gap: 20px; align-items: center; }
.form-group.radio-group label { margin-bottom: 0; }
.settings-centered-buttons {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    width: 100%;
}
.settings-centered-buttons > .btn, .settings-centered-buttons > .btn-primary {
    width: 100%;
    margin-bottom: 0;
    margin-left: auto;
    margin-right: auto;
}

.modal-window .btn {
    width: 100%;
    margin-top: 10px;
}

/* New Glassmorphism Button Styles */
.btn {
    padding: 15px;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.4);
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-align: center;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
}
.btn:active { transform: scale(0.97); }

.btn-primary { background-color: rgba(var(--primary-color-rgb), 0.7); color: var(--text-color); }
.btn-primary:hover { background-color: rgba(var(--primary-color-rgb), 0.85); box-shadow: 0 4px 20px rgba(var(--primary-color-rgb), 0.4); }
label.btn-primary { color: var(--text-color) !important; display: block; text-align: center; }

.btn-secondary { background-color: rgba(var(--accent-color-rgb), 0.7); color: var(--white-color); margin-bottom: 15px; text-shadow: 0 1px 2px rgba(0,0,0,0.2); }
.btn-secondary:hover { background-color: rgba(var(--accent-color-rgb), 0.85); box-shadow: 0 4px 20px rgba(var(--accent-color-rgb), 0.4); }

.btn-neutral { background-color: rgba(var(--neutral-color-rgb), 0.6); color: var(--white-color); }
.btn-neutral:hover { background-color: rgba(var(--neutral-color-rgb), 0.75); }

.btn-danger { background-color: rgba(var(--danger-color-rgb), 0.7); color: white; }
.btn-danger:hover { background-color: rgba(var(--danger-color-rgb), 0.85); }

.btn-small { padding: 8px 16px; font-size: 14px; width: auto; }
.btn .spinner { display: none; width: 16px; height: 16px; border: 2px solid rgba(255, 255, 255, 0.5); border-top-color: var(--white-color); border-radius: 50%; animation: spin 1s linear infinite; }
.btn.loading .spinner { display: block; }
.btn.loading .btn-text { display: none; }

.wallpaper-preview { width: 100%; aspect-ratio: 9 / 16; max-height: 400px; border-radius: var(--border-radius); margin-bottom: 15px; background-size: cover; background-position: center; border: 3px dashed var(--primary-color); display: flex; align-items: center; justify-content: center; color: var(--secondary-color); font-style: italic; background-color: var(--bg-color); }
.toast { position: absolute; bottom: 80px; left: 50%; transform: translateX(-50%); background-color: rgba(0, 0, 0, 0.7); color: white; padding: 10px 20px; border-radius: 15px; font-size: 14px; opacity: 0; visibility: hidden; transition: opacity 0.5s, visibility 0.5s; z-index: 1000; }
.toast.show { opacity: 1; visibility: visible; }
#world-book-selection-modal, #invite-member-modal, #character-select-modal, #profile-select-modal, #theme-select-modal, #heart-voice-modal, #listen-together-modal, #lyrics-modal { z-index: 102; }
#batch-sticker-modal, #diary-settings-modal { z-index: 103; }
.modal-window { width: 90%; max-width: 380px; }
#world-book-selection-list, #invite-member-selection-list, #character-selection-list, #profile-selection-list, #listen-together-char-list { list-style: none; padding: 0; margin: 0; max-height: 40vh; overflow-y: auto; }
.selection-item { display: flex; align-items: center; padding: 12px 5px; border-bottom: 1px solid #f0f0f0; cursor: pointer; transition: background-color 0.2s; }
.selection-item:hover { background-color: var(--bg-color); }
.selection-item:last-child { border-bottom: none; }
.selection-item img { width: 40px; height: 40px; border-radius: 50%; object-fit: cover; margin-right: 10px; }
.selection-item span { font-weight: 500; color: var(--text-color); }
.diary-selection-item { display: flex; align-items: center; padding: 12px 5px; border-bottom: 1px solid #f0f0f0; cursor: pointer; transition: background-color 0.2s; }
.diary-selection-item:hover { background-color: var(--bg-color); }
.diary-selection-item:last-child { border-bottom: none; }
.diary-selection-item img { width: 40px; height: 40px; border-radius: 50%; object-fit: cover; margin-right: 10px; }
.diary-selection-item span { font-weight: 500; color: var(--text-color); }
.member-selection-list { list-style: none; padding: 0; margin: 15px 0; max-height: 40vh; overflow-y: auto; }
.member-selection-item { display: flex; align-items: center; padding: 10px 5px; border-bottom: 1px solid #f0f0f0; }
.member-selection-item:last-child { border-bottom: none; }
.member-selection-item input[type="checkbox"] { margin-right: 15px; width: 20px; height: 20px; flex-shrink: 0; }
.member-selection-item img { width: 40px; height: 40px; border-radius: 50%; object-fit: cover; margin-right: 10px; }
.member-selection-item label { font-weight: 500; color: var(--text-color); }
#group-settings-sidebar .group-avatar-setting { display: flex; align-items: center; gap: 15px; margin-bottom: 15px; }
#group-settings-sidebar .group-avatar-preview { width: 60px; height: 60px; border-radius: 10px; object-fit: cover; border: 2px solid var(--primary-color); cursor: pointer; }
#group-settings-sidebar .group-members-list { display: grid; grid-template-columns: repeat(auto-fill, minmax(60px, 1fr)); gap: 15px; margin-top: 10px; }
#group-settings-sidebar .group-member, #group-settings-sidebar .add-member-btn { display: flex; flex-direction: column; align-items: center; cursor: pointer; }
#group-settings-sidebar .group-member img { width: 50px; height: 50px; border-radius: 50%; object-fit: cover; margin-bottom: 5px; border: 2px solid #eee; }
#group-settings-sidebar .add-member-btn .add-icon { width: 50px; height: 50px; border-radius: 50%; border: 2px dashed #ccc; display: flex; align-items: center; justify-content: center; font-size: 24px; color: #ccc; margin-bottom: 5px; transition: all 0.2s ease; }
#group-settings-sidebar .add-member-btn:hover .add-icon { color: var(--primary-color); border-color: var(--primary-color); }
#group-settings-sidebar .group-member span, #group-settings-sidebar .add-member-btn span { font-size: 12px; text-align: center; color: var(--text-color); }
#beautify-screen h3 { text-align: center; color: var(--secondary-color); margin-top: 0; margin-bottom: 15px; font-weight: 600; }
#beautify-screen .icon-custom-item { display: flex; align-items: center; gap: 15px; margin-bottom: 15px; padding-bottom: 15px; border-bottom: 1px solid #f0f0f0; }
#beautify-screen .icon-custom-item:last-child { border-bottom: none; }
#beautify-screen .icon-preview-label { cursor: pointer; display: flex; flex-direction: column; align-items: center; }
#beautify-screen .icon-preview { width: 50px; height: 50px; border-radius: 12px; object-fit: cover; flex-shrink: 0; margin-bottom: 5px; }
#beautify-screen .icon-details { flex-grow: 1; display:flex; flex-direction:column; align-items:center; }
#beautify-screen .icon-details p { margin: 0 0 8px 0; font-weight: 600; }
#beautify-screen .reset-icon-btn { background: #e0e0e0; color: #555; border: none; border-radius: 8px; padding: 6px 10px; font-size: 12px; cursor: pointer; margin-top: 5px; }
.beautify-section { margin-top: 30px; padding-top: 20px; border-top: 1px solid #f0f0f0; }
#wallpaper-controls { margin-top: 15px; transition: opacity 0.3s ease; }
.color-picker-group { display: flex; align-items: center; gap: 15px; margin-top: 10px; }
.color-picker-group label { font-weight: 600; color: var(--secondary-color); }
#theme-color-picker { -webkit-appearance: none; -moz-appearance: none; appearance: none; width: 60px; height: 40px; background-color: transparent; border: 2px solid #eee; border-radius: 8px; cursor: pointer; }
#theme-color-picker::-webkit-color-swatch { border-radius: 6px; border: none; }
#theme-color-picker::-moz-color-swatch { border-radius: 6px; border: none; }
.qq-nav { flex-shrink: 0; display: flex; justify-content: space-around; padding: 5px 0 calc(5px + env(safe-area-inset-bottom)); border-top: 1px solid #eee; background-color: #f7f7f7; }
.nav-item { display: flex; flex-direction: column; align-items: center; justify-content: center; font-size: 10px; color: #888; cursor: pointer; flex-grow: 1; padding: 5px 0; }
.nav-item svg { width: 28px; height: 28px; margin-bottom: 2px; transition: color 0.2s, transform 0.2s; }
.nav-item .icon-outline { display: block; fill: #888; }
.nav-item .icon-filled { display: none; fill: var(--secondary-color); }
.nav-item.active { color: var(--secondary-color); }
.nav-item.active .icon-outline { display: none; }
.nav-item.active .icon-filled { display: block; }
.nav-item:active svg { transform: scale(0.9); }
.qq-tab-panel { display: none; flex-direction: column; height: 100%; overflow: hidden; }
.qq-tab-panel.active { display: flex; }
.qq-tab-panel .content { flex-grow: 1; overflow-y: auto; }
.contact-actions { display: flex; gap: 10px; }
#moments-screen-panel .content { padding: 0; }
#moments-settings-screen .avatar-setting { flex-direction: row; }
#moments-settings-screen .form-group > label { text-align: center; }
.moments-header { position: relative; width: 100%; height: 250px; margin-bottom: 20px; }
.moments-header-bg { width: 100%; height: 100%; object-fit: cover; }
.moments-user-info { position: absolute; bottom: -15px; right: 20px; display: flex; align-items: center; gap: 15px; }
.moments-user-name { color: white; font-size: 18px; font-weight: bold; text-shadow: 0 1px 3px rgba(0,0,0,0.6); }
.moments-user-avatar { width: 70px; height: 70px; border-radius: 10px; border: 3px solid white; object-fit: cover; }
#moments-feed-container { list-style: none; padding: 0; margin: 0; }
.moment-item { display: flex; padding: 15px; border-bottom: 1px solid #eee; gap: 12px; }
.moment-item:last-child { border-bottom: none; }
.moment-item-avatar { width: 42px; height: 42px; border-radius: 8px; object-fit: cover; flex-shrink: 0; }
.moment-item-main { display: flex; flex-direction: column; flex-grow: 1; gap: 8px; }
.moment-item-name { font-weight: 600; color: var(--secondary-color); margin: 0; }
.moment-item-content { margin: 0; line-height: 1.6; color: var(--text-color); word-break: break-word; }
.moment-item-image { width: auto; max-width: 200px; height: auto; border-radius: 10px; margin-top: 5px; cursor: pointer; object-fit: cover; max-height: 250px; }
.moment-item-audio { margin-top: 10px; }
.moment-item-footer { display: flex; justify-content: space-between; align-items: center; margin-top: 10px; }
.moment-item-time { font-size: 13px; color: #aaa; }
.moment-item-actions { position: relative; }
.moment-action-btn { background-color: #f0f0f0; border: none; border-radius: 5px; width: 30px; height: 20px; cursor: pointer; font-weight: bold; color: var(--secondary-color); display: flex; align-items: center; justify-content: center; }
.moment-actions-popup { display: none; position: absolute; right: 105%; bottom: -5px; background: #4c4c4c; border-radius: 5px; z-index: 10; overflow: hidden; transform-origin: right; animation: scaleIn 0.1s ease; }
.moment-actions-popup.active { display: flex; }
@keyframes scaleIn { from { transform: scale(0); } to { transform: scale(1); } }
.moment-actions-popup button { background: none; border: none; color: white; padding: 8px 12px; cursor: pointer; display: flex; align-items: center; gap: 5px; font-size: 13px; white-space: nowrap; }
.moment-actions-popup button:hover { background-color: #333; }
.moment-actions-popup .like-btn.liked, .moment-actions-popup .like-btn:active { color: #E53935; }
.moment-actions-popup svg { width: 16px; height: 16px; fill: white; }
.moment-interactions { margin-top: 10px; background: var(--moments-interactions-bg); border-radius: 5px; font-size: 14px; overflow: hidden; }
.moment-likes { padding: 8px 12px; display: flex; align-items: center; gap: 8px; }
.moment-likes .like-icon { fill: var(--secondary-color); width: 16px; height: 16px; flex-shrink: 0; }
.moment-likes span { color: var(--secondary-color); font-weight: 500; line-height: 1.4; }
.moment-comments-list { list-style: none; padding: 0; margin: 0; }
.moment-likes + .moment-comments-list { border-top: 1px solid #e5e5e5; }
.moment-comment-item { padding: 8px 12px; line-height: 1.5; border-bottom: 1px solid #e5e5e5; cursor: pointer; }
.moment-comment-item:hover { background-color: #efefef; }
.moment-comment-item:last-child { border-bottom: none; }
.moment-comment-name { color: var(--secondary-color); font-weight: 600; }
.moment-comment-reply-to { color: var(--text-color); }
.theme-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(60px, 1fr)); gap: 15px; }
.theme-item { display: flex; flex-direction: column; align-items: center; cursor: pointer; }
.theme-item .color-preview { width: 40px; height: 40px; border-radius: 50%; margin-bottom: 5px; border: 2px solid #eee; display: flex; align-items: center; justify-content: center; overflow: hidden; transition: border-color 0.2s, transform 0.2s; }
.theme-item .color-preview .sent-half { width: 50%; height: 100%; }
.theme-item .color-preview .received-half { width: 50%; height: 100%; }
.theme-item.selected .color-preview { border-color: var(--secondary-color); transform: scale(1.1); }
.theme-item span { font-size: 12px; }
#top-notification-bar {
    position: fixed;
    top: -100px;
    left: 50%;
    transform: translateX(-50%);
    width: calc(100% - 30px);
    max-width: 390px;
    background-color: rgba(253, 251, 246, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 0 0 18px 18px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    padding: 12px 15px;
    z-index: 9999;
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    transition: top 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}
#top-notification-bar.show {
    top: 0;
}
#top-notification-avatar {
    width: 40px;
    height: 40px;
    border-radius: 12px;
    object-fit: cover;
}
#top-notification-text {
    flex-grow: 1;
    overflow: hidden; /* Added to contain the preview text */
}
#top-notification-title {
    font-weight: 600;
    color: var(--text-color);
    font-size: 15px;
}
#top-notification-preview {
    font-size: 13px;
    color: #888;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* === Call System Styles === */
#incoming-call-modal .modal-window {
    background-color: rgba(40, 40, 40, 0.85);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 20px;
    width: 280px;
    padding: 30px 20px;
    text-align: center;
    color: white;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}
.caller-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 12px;
    border: 3px solid rgba(255,255,255,0.5);
}
.caller-name { font-size: 20px; font-weight: 600; margin-bottom: 5px; }
.caller-text { font-size: 14px; color: #ccc; margin-bottom: 30px; }
.incoming-call-actions { display: flex; justify-content: space-around; align-items: center; }
.action-button-wrapper { display: flex; flex-direction: column; align-items: center; gap: 8px; font-size: 13px; color: #e0e0e0; }
.call-action-btn { width: 60px; height: 60px; border-radius: 50%; border: none; cursor: pointer; background-size: 50%; background-repeat: no-repeat; background-position: center; transition: transform 0.2s, box-shadow 0.2s; box-shadow: 0 4px 10px rgba(0,0,0,0.2); display: flex; align-items: center; justify-content: center; }
.call-action-btn:active { transform: scale(0.9); }
.call-action-btn.decline { background-color: #ff3b30; }
.call-action-btn.accept { background-color: #4cd964; animation: pulse 1.5s infinite; }
@keyframes pulse { 0% { box-shadow: 0 0 0 0 rgba(76, 217, 100, 0.7); } 70% { box-shadow: 0 0 0 15px rgba(76, 217, 100, 0); } 100% { box-shadow: 0 0 0 0 rgba(76, 217, 100, 0); } }

#outgoing-call-screen { 
    background-color: #1c1c1e; color: white; justify-content: center; align-items: center; 
}
.call-content { display: flex; flex-direction: column; align-items: center; text-align: center; }
#outgoing-call-screen .caller-avatar { width: 100px; height: 100px; }
#outgoing-call-screen .caller-name { font-size: 24px; }
#outgoing-call-screen .caller-text { margin-top: 10px; }

#call-screen {
    background-color: #1c1c1e;
    color: white;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
}
#call-screen-main-view {
    position: absolute;
    top: 0; left: 0; width: 100%; height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    padding-top: 85px;
    text-align: center;
    z-index: 3;
}
#call-screen-main-view::before {
    content: '';
    position: absolute;
    top: 0; left: 0; width: 100%; height: 100%;
    background-image: var(--bg-image);
    background-size: cover;
    background-position: center;
    filter: blur(20px) brightness(0.5);
    transform: scale(1.1);
}
#participant-avatars-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-bottom: 15px;
    z-index: 1;
}
.participant-avatar-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
}
.participant-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid rgba(255, 255, 255, 0.3);
    margin-bottom: 5px;
    transition: box-shadow 0.3s ease;
}
.participant-avatar.speaking {
    box-shadow: 0 0 0 4px var(--primary-color);
}
.participant-name {
    font-size: 16px;
    font-weight: 500;
}

#main-participant-name { font-size: 24px; font-weight: 600; z-index: 1; }
#main-participant-status { font-size: 14px; color: #ccc; z-index: 1; margin-top: 5px;}

#call-screen-self-view {
    position: absolute;
    top: 60px;
    right: 20px;
    width: 100px;
    height: 150px;
    background-color: #333;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.3);
    overflow: hidden;
    cursor: grab;
    z-index: 5;
    display: flex;
    align-items: center;
    justify-content: center;
}
#self-view-avatar {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.call-header { 
    position: absolute; top: 0; left: 0; right: 0; padding-top: 20px; text-align: center; z-index: 10;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
}
#call-timer { font-size: 16px; letter-spacing: 1px; background-color: rgba(0,0,0,0.3); padding: 4px 10px; border-radius: 10px; display: inline-block;}

.call-transcript-area { 
    position: absolute;
    bottom: 120px;
    left: 15px; right: 15px;
    height: 35%; 
    max-height: 250px;
    overflow-y: auto; 
    padding: 15px; 
    display: flex; 
    flex-direction: column; 
    gap: 12px; 
    box-sizing: border-box;
    z-index: 4;
    -webkit-mask-image: linear-gradient(to top, black 80%, transparent 100%);
    mask-image: linear-gradient(to top, black 80%, transparent 100%);
}

.call-bubble { padding: 10px 15px; border-radius: 18px; max-width: 85%; line-height: 1.6; word-break: break-word; white-space: pre-wrap; box-shadow: 0 2px 5px rgba(0,0,0,0.2);}
.call-bubble.ai-speech { background-color: rgba(50, 50, 50, 0.7); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); align-self: flex-start; }
.call-bubble.user-input { background-color: #3a3a3c; align-self: flex-end; text-align: left; }

.call-controls { 
    position: absolute; 
    bottom: 0; left: 0; right: 0; 
    display: flex; 
    justify-content: space-between;
    align-items: center;
    padding: 20px; 
    padding-bottom: 40px; 
    gap: 15px;
    z-index: 10; 
}
.call-input-container {
    display: flex;
    flex-grow: 1;
    background-color: rgba(255, 255, 255, 0.15);
    border-radius: 22px;
    padding: 4px;
}
#call-input-text {
    flex-grow: 1;
    border: none;
    background: transparent;
    color: white;
    padding: 0 15px;
    font-size: 16px;
}
#call-input-text:focus {
    outline: none;
}
#call-input-send-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border: none;
    background-color: var(--primary-color);
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.home-signature {
    font-family: 'Comic Sans MS', 'Chalkduster', 'Handwriting', cursive;
    font-size: 16px;
    margin-top: 10px;
    padding: 5px 15px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    display: inline-block;
    color: var(--text-color);
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    cursor: pointer;
}
#home-screen.day-mode .home-signature {
    color: var(--white-color);
    background-color: rgba(0, 0, 0, 0.2);
    text-shadow: 0 1px 3px rgba(0,0,0,0.4);
}
.dock .app-icon .app-name {
    display: none;
}
.dock .app-icon .icon-img {
    margin-bottom: 0;
}

/* Music Player Styles */
#music-screen .content {
    padding: 0;
    display: flex;
    flex-direction: column;
    background-color: #f8f8f8;
}
#music-player-ui {
    padding: 20px 20px 0 20px;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    align-items: center;
    justify-content: space-around;
    background-size: cover;
    background-position: center;
    color: #333;
}
    #music-player-ui.has-bg {
    color: white;
    text-shadow: 0 1px 3px rgba(0,0,0,0.5);
}
#music-player-ui.has-bg #song-info .artist {
    color: #eee;
}
    #music-player-ui.has-bg .control-btn, #music-player-ui.has-bg .action-row .control-btn {
    color: white;
}
#album-art {
    width: 250px;
    height: 250px;
    border-radius: 50%;
    object-fit: cover;
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
    border: 8px solid rgba(255,255,255,0.8);
}
#album-art.playing {
    animation: spin 8s linear infinite;
}
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}
#song-info {
    text-align: center;
    width: 100%;
}
#song-info .title {
    font-size: 24px;
    font-weight: bold;
    margin: 0;
}
#song-info .artist {
    font-size: 16px;
    color: #888;
    margin-top: 8px;
}
.action-row {
    width: 100%;
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 0 20px;
}
.action-row .control-btn {
    background: none; border: none; cursor: pointer; color: #555;
}
.action-row .control-btn svg { width: 28px; height: 28px; fill: currentColor; }
.action-row .control-btn.liked svg { fill: #E53935; }

#music-progress-container {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 12px;
    color: #888;
}
#progress-bar {
    flex-grow: 1;
    height: 4px;
    background-color: #ddd;
    border-radius: 2px;
    cursor: pointer;
}
#progress {
    height: 100%;
    width: 0;
    background-color: var(--secondary-color);
    border-radius: 2px;
}
#music-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 30px;
    width: 100%;
}
.control-btn {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-color);
}
.control-btn svg {
    width: 32px;
    height: 32px;
    fill: currentColor;
}
#play-pause-btn svg {
    width: 64px;
    height: 64px;
}
#music-footer-controls {
    display: flex;
    justify-content: space-around;
    align-items: center;
    width: 100%;
    padding: 15px 20px calc(15px + env(safe-area-inset-bottom));
}
    #music-footer-controls .control-btn svg {
    width: 26px;
    height: 26px;
}
#playlist-manage-screen .list-item {
    justify-content: space-between;
}
.delete-song-btn {
    background: none;
    border: none;
    cursor: pointer;
    color: #ff3b30;
    font-size: 24px;
    padding: 5px;
}
.drag-handle {
    cursor: grab;
    color: #ccc;
    margin-right: 15px;
}
.list-item.dragging {
    opacity: 0.5;
    background: #f0f0f0;
}

/* Dynamic Island Styles */
#dynamic-island {
    position: absolute;
    top: 15px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10000;
    background-color: rgba(20, 20, 20, 0.9);
    color: white;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.2, 0.8, 0.2, 1);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    overflow: hidden;
    width: 200px; /* MODIFIED: Set a smaller default width */
    height: 42px; /* MODIFIED: Set a fixed height for collapsed state */
}
#dynamic-island.expanded {
        width: 280px; /* MODIFIED: Set expanded width */
        height: auto; /* MODIFIED: Allow height to grow */
}

.island-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 100%;
}
.island-collapsed {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    width: 100%; /* MODIFIED: Take full width of parent */
    height: 100%; /* MODIFIED: Take full height of parent */
}
#dynamic-island.expanded .island-collapsed {
    display: none;
}

#island-album-art-collapsed {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    object-fit: cover;
}
#island-song-title-collapsed {
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex-grow: 1;
}
#island-play-icon-collapsed {
    font-size: 12px;
    width: 20px;
    text-align: center;
}

.island-expanded {
    display: none; /* MODIFIED: Hidden by default */
    flex-direction: column;
    padding: 15px;
    width: 100%; /* MODIFIED: Take full width of parent */
}
#dynamic-island.expanded .island-expanded {
    display: flex; /* MODIFIED: Shown when expanded */
}

.island-expanded-info {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 10px;
}
#island-album-art-expanded {
    width: 45px;
    height: 45px;
    border-radius: 8px;
    object-fit: cover;
}
#island-song-details-expanded {
    overflow: hidden;
}
#island-song-title-expanded {
    font-size: 16px;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
#island-song-artist-expanded {
    font-size: 13px;
    color: #ccc;
}
#island-progress-bar {
    width: 100%;
    height: 3px;
    background-color: #555;
    border-radius: 1.5px;
    margin-bottom: 10px;
}
#island-progress {
    width: 0;
    height: 100%;
    background-color: white;
    border-radius: 1.5px;
}
#island-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
#island-controls button {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
}
#island-controls svg {
    width: 24px;
    height: 24px;
}
#island-play-pause-btn svg {
    width: 32px;
    height: 32px;
}

/* NEW Music Card Styles */
.music-share-card, .listen-together-card {
    width: 230px;
    border-radius: 12px;
    margin: 0 8px;
    overflow: hidden;
    position: relative;
    box-shadow: 0 4px 12px rgba(90, 90, 90, 0.2);
    color: white;
    background-color: #333;
    cursor: pointer;
}
.music-card-bg {
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    background-size: cover;
    background-position: center;
    filter: blur(8px) brightness(0.6);
    transform: scale(1.1);
    z-index: 1;
}
.music-card-content {
    padding: 15px;
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
    height: 100%;
}
.music-card-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 10px;
}
.music-card-album-art {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    object-fit: cover;
    flex-shrink: 0;
}
.music-card-info .title {
    font-size: 16px;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.music-card-info .artist {
    font-size: 13px;
    opacity: 0.8;
}
.music-card-footer {
    font-size: 12px;
    margin-top: auto;
    padding-top: 8px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    opacity: 0.7;
}
.listen-together-card .music-card-footer {
    background-color: rgba(0,0,0,0.3);
    margin: 10px -15px -15px;
    padding: 10px 15px;
    text-align: center;
    border-top: none;
    font-weight: bold;
    opacity: 1;
}

/* NEW Lyrics Styles */
#lyrics-modal .modal-window {
    width: 90%;
    max-width: 400px;
}
#lyrics-content {
    max-height: 50vh;
    overflow-y: auto;
    text-align: center;
    line-height: 2;
    font-size: 16px;
    color: var(--text-color);
    scroll-behavior: smooth;
}
#lyrics-content .lyric-line {
    transition: all 0.3s ease;
    opacity: 0.6;
}
#lyrics-content .lyric-line.active {
    opacity: 1;
    font-weight: bold;
    transform: scale(1.1);
    color: var(--secondary-color);
}
#edit-lyrics-textarea {
    width: 100%;
    height: 40vh;
    margin-top: 15px;
}
/* NEW Collection & Diary Styles */
#collection-screen .list-item, #diary-bookshelf-screen .list-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
}
.collection-source, .collection-time {
    font-size: 12px;
    color: #888;
}
.collection-content {
    width: 100%;
    padding: 10px;
    background-color: #f5f5f5;
    border-radius: 8px;
}
#diary-bookshelf {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 20px;
    padding: 20px;
}
.diary-book {
    aspect-ratio: 3/4;
    background-color: #D2B48C;
    border-radius: 8px 4px 4px 8px;
    box-shadow: 5px 5px 15px rgba(0,0,0,0.2);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 10px;
    color: white;
    cursor: pointer;
    position: relative;
    background-size: cover;
    background-position: center;
}
.diary-book::before {
    content: '';
    position: absolute;
    left: 5px;
    top: 0;
    bottom: 0;
    width: 5px;
    background: rgba(0,0,0,0.1);
}
.diary-book-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: 2px solid white;
    object-fit: cover;
    margin-bottom: 10px;
}
.diary-book-name {
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0,0,0,0.5);
}
.diary-book-edit-btn {
    position: absolute;
    bottom: 5px;
    right: 5px;
    font-size: 10px;
    background: rgba(0,0,0,0.4);
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 6px;
    cursor: pointer;
}

#diary-view-screen .content {
    padding: 0;
    perspective: 1200px;
    background: #f0e9e0;
    background-image: none;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}
.book-container {
    width: 100%;
    height: 100%;
    position: relative;
    transform-style: preserve-3d;
    background-size: cover;
    background-position: center;
}
.book-page {
    position: absolute;
    width: 100%;
    height: 100%;
    background: rgba(253, 251, 246, 0.95);
    padding: 20px;
    overflow-y: auto;
    backface-visibility: hidden;
    transform-origin: left center;
    display: block;
    visibility: hidden;
    line-height: 1.8;
    border: 1px solid #ddd;
    font-family: 'Georgia', serif;
}
.book-page.active { 
    visibility: visible;
}
.book-page.turning-forward-out {
    visibility: visible;
    animation: turn-page-forward 0.8s forwards;
    transform-origin: right center;
}
.book-page.turning-forward-in {
    visibility: visible;
    animation: turn-page-forward-in 0.8s forwards;
    transform-origin: left center;
}
.book-page.turning-backward-out {
    visibility: visible;
    animation: turn-page-backward 0.8s forwards;
    transform-origin: left center;
}
.book-page.turning-backward-in {
    visibility: visible;
    animation: turn-page-backward-in 0.8s forwards;
    transform-origin: right center;
}
@keyframes turn-page-forward {
    from { transform: rotateY(0deg); }
    to { transform: rotateY(-180deg); }
}
@keyframes turn-page-forward-in {
    from { transform: rotateY(180deg); }
    to { transform: rotateY(0deg); }
}
@keyframes turn-page-backward {
    from { transform: rotateY(0deg); }
    to { transform: rotateY(180deg); }
}
@keyframes turn-page-backward-in {
    from { transform: rotateY(-180deg); }
    to { transform: rotateY(0deg); }
}
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    border-bottom: 1px solid #ccc;
    padding-bottom: 10px;
    margin-bottom: 15px;
}
.page-date { font-size: 14px; font-weight: bold; }
.page-meta { font-size: 12px; color: #888; }
.page-content { white-space: pre-wrap; }
.diary-nav {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 90%;
    display: flex;
    justify-content: space-between;
    z-index: 10;
}
.diary-nav button {
    background: rgba(var(--primary-color-rgb), 0.8);
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    color: var(--text-color);
    font-weight: bold;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}
.diary-nav button:disabled { background: #ccc; }
#forwarded-content-modal .message-wrapper {
        width: 100%;
        max-width: 100%;
}
    #forwarded-content-modal .message-bubble {
        max-width: 100%;
    }
/* NEW Diary Settings Modal Styles */
.diary-settings-section {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}
.diary-settings-section:last-child {
    border-bottom: none;
    padding-bottom: 0;
}
.diary-settings-section h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: var(--secondary-color);
}
.diary-settings-list {
    max-height: 20vh;
    overflow-y: auto;
}
.diary-settings-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}
.diary-settings-item label {
    display: flex;
    align-items: center;
    gap: 10px;
}
.diary-settings-item img {
    width: 30px;
    height: 30px;
    border-radius: 50%;
}