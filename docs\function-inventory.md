# AIChatBox JavaScript 函数功能清单

## 核心系统函数

### 应用初始化
- `init()` - 应用程序主初始化函数
- `loadData()` - 从localStorage加载应用数据
- `saveData()` - 保存应用数据到localStorage
- `injectHTML()` - 注入HTML模板到各个容器

### 全局工具函数
- `getEl(id)` - 获取DOM元素（带缓存机制）
- `showToast(message)` - 显示提示消息
- `switchScreen(targetId)` - 屏幕切换功能
- `pad(num)` - 数字补零工具函数

### 颜色和样式工具
- `adjustColor(hex, percent)` - 调整十六进制颜色亮度
- `lightenRgba(color, percent)` - 调亮RGBA颜色
- `applyThemeColor(hexColor)` - 应用全局主题色
- `applyGlobalFont(fontUrl)` - 应用全局字体

## 界面管理函数

### 主屏幕系统
- `setupHomeScreen()` - 设置主屏幕界面和事件
- `updateClock()` - 更新时钟显示
- `applyWallpaper(url)` - 应用壁纸
- `applyHomeScreenMode(mode)` - 应用主屏幕模式（日间/夜间）

### 通用UI组件
- `createContextMenu(items, x, y)` - 创建右键菜单
- `removeContextMenu()` - 移除右键菜单

## 聊天系统函数

### QQ应用核心
- `setupQQApp()` - 设置QQ应用主界面和导航
- `renderChatList()` - 渲染聊天列表
- `renderContactsList()` - 渲染联系人列表
- `renderMeProfilesList()` - 渲染"我"的身份列表

### 聊天室功能
- `setupChatRoom()` - 设置聊天室界面和事件
- `openChatRoom(chatId, chatType)` - 打开聊天室
- `renderChatHistory(page)` - 渲染聊天历史
- `sendMessage(text, type, extraData)` - 发送消息
- `addMessageToHistory(message, chatId, chatType)` - 添加消息到历史
- `generateAIResponse(chatId, chatType, userMessage)` - 生成AI回复

### 消息处理
- `renderMessage(message, isGroup)` - 渲染单条消息
- `handleMessageInteraction()` - 处理消息交互（长按、多选等）
- `deleteSelectedMessages()` - 删除选中的消息
- `collectSelectedMessages()` - 收藏选中的消息
- `shareSelectedMessages()` - 分享选中的消息

### 聊天设置
- `setupChatSettings()` - 设置聊天配置界面
- `saveChatSettings(chatId, chatType)` - 保存聊天设置
- `applyChatTheme(chatId, chatType)` - 应用聊天主题

## AI集成函数

### API管理
- `setupApiSettingsApp()` - 设置API配置界面
- `fetchAvailableModels()` - 获取可用模型列表
- `saveApiProfile()` - 保存API配置
- `callAIAPI(messages, chatId, chatType)` - 调用AI API

### 记忆核心
- `setupMemoryCoreApp()` - 设置记忆核心界面
- `setupMemoryCoreSettingsApp()` - 设置记忆核心配置
- `renderMemoryCoreList()` - 渲染记忆列表
- `extractMemoryFromChat(chatId, chatType)` - 从聊天中提取记忆
- `saveMemoryEntry()` - 保存记忆条目

## 媒体系统函数

### 音乐播放器
- `setupMusicApp()` - 设置音乐应用界面
- `setupPlaylistManagement()` - 设置播放列表管理
- `renderPlaylist()` - 渲染播放列表
- `playMusic(index)` - 播放指定音乐
- `pauseMusic()` - 暂停音乐
- `nextSong()` - 下一首歌
- `prevSong()` - 上一首歌
- `updateMusicProgress()` - 更新播放进度

### 动态岛功能
- `setupDynamicIsland()` - 设置动态岛
- `showDynamicIsland()` - 显示动态岛
- `hideDynamicIsland()` - 隐藏动态岛
- `updateDynamicIslandContent(songInfo)` - 更新动态岛内容

### 语音和图片处理
- `setupVoiceMessageSystem()` - 设置语音消息系统
- `setupImageUpload()` - 设置图片上传
- `compressImage(file, options)` - 压缩图片
- `generateVoiceMessage(text)` - 生成语音消息

## 通信功能函数

### 通话系统
- `setupCallSystem()` - 设置通话系统
- `initiateCall(contactId, isGroup)` - 发起通话
- `acceptCall()` - 接听电话
- `hangUpCall()` - 挂断电话
- `updateCallTimer()` - 更新通话计时

### 表情包系统
- `setupStickerSystem()` - 设置表情包系统
- `renderStickerGrid()` - 渲染表情包网格
- `addSticker(name, url)` - 添加表情包
- `parseAndAddStickers(rawText, isDefault)` - 批量添加表情包
- `toggleStickerModal(show)` - 切换表情包弹窗

### 钱包和支付系统
- `setupWalletSystem()` - 设置钱包系统
- `sendTransfer(amount, remark, recipientId)` - 发送转账
- `sendRedPacket(amount, count, remark)` - 发送红包
- `processTransfer(messageId, action)` - 处理转账

### 礼物和位置系统
- `setupGiftSystem()` - 设置礼物系统
- `setupLocationSystem()` - 设置位置系统
- `sendGift(description)` - 发送礼物
- `sendLocation(name, address)` - 发送位置

## 内容管理函数

### 世界书系统
- `setupWorldBookApp()` - 设置世界书应用
- `renderWorldBookList()` - 渲染世界书列表
- `saveWorldBookEntry()` - 保存世界书条目
- `deleteWorldBookEntry(id)` - 删除世界书条目

### 朋友圈功能
- `setupMomentsApp()` - 设置朋友圈应用
- `setupMomentPosting()` - 设置朋友圈发布
- `renderMomentsList()` - 渲染朋友圈列表
- `createMoment(text, image)` - 创建朋友圈动态
- `generateAIMoment()` - 生成AI朋友圈动态

### 日记系统
- `setupDiarySystem()` - 设置日记系统
- `renderDiaryBookshelf()` - 渲染日记书架
- `generateDiaryEntry(characterId)` - 生成日记条目
- `renderDiaryBook(characterId)` - 渲染日记本

### 收藏系统
- `setupFileAndCollectionSystem()` - 设置文件和收藏系统
- `addToCollection(messageData)` - 添加到收藏
- `renderCollectionList()` - 渲染收藏列表

## 个性化函数

### 美化系统
- `setupBeautifyApp()` - 设置美化应用
- `renderCustomizeForm()` - 渲染自定义表单
- `applyMusicPlayerCustomization()` - 应用音乐播放器自定义

### 字体管理
- `setupFontSettingsApp()` - 设置字体配置应用

## 群聊系统函数

### 群聊核心
- `setupGroupChatSystem()` - 设置群聊系统
- `createGroup(name, memberIds)` - 创建群聊
- `renderGroupMembersList(groupId)` - 渲染群成员列表
- `addMemberToGroup(groupId, memberId)` - 添加成员到群聊
- `removeMemberFromGroup(groupId, memberId)` - 从群聊移除成员

## 定时任务和调度函数

### 自动化任务
- `proactiveChatScheduler()` - 主动聊天调度器
- `diaryWritingScheduler()` - 日记写作调度器
- `momentsPostingScheduler()` - 朋友圈发布调度器

### 通知系统
- `setupNotificationSystem()` - 设置通知系统
- `showTopNotification(title, preview, avatar, onClick)` - 显示顶部通知

## 数据处理函数

### 数据导入导出
- `exportAllData()` - 导出所有数据
- `importAllData(data)` - 导入所有数据
- `migrateData()` - 数据迁移处理

### 配置管理
- `getActiveApiProfile()` - 获取当前API配置
- `updateCharacterStatus(characterId, status)` - 更新角色状态
- `updateUnreadCount(chatId, chatType, increment)` - 更新未读消息数

## 事件处理函数

### 全局事件
- 文档点击事件处理
- 屏幕导航事件处理
- 文件上传事件处理
- 表单提交事件处理

### 专用事件处理器
- 长按事件处理
- 多选模式事件处理
- 拖拽事件处理
- 触摸事件处理

## 实用工具函数

### 时间处理
- 时间格式化函数
- 相对时间计算
- 定时器管理

### 文本处理
- 消息文本格式化
- HTML转义处理
- 链接检测和处理

### 网络请求
- API请求封装
- 错误处理
- 重试机制

## 总计统计
- **总函数数量**: 约150-200个函数
- **核心系统函数**: ~20个
- **界面管理函数**: ~15个  
- **聊天系统函数**: ~30个
- **AI集成函数**: ~15个
- **媒体系统函数**: ~25个
- **通信功能函数**: ~20个
- **内容管理函数**: ~20个
- **个性化函数**: ~10个
- **群聊系统函数**: ~15个
- **其他工具函数**: ~30个