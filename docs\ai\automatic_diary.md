# AI功能文档：自动撰写日记

## 1. 功能概述

### 核心目标
让AI角色能够基于与用户的日常互动，定期（或在特定条件下）自动生成一篇充满个人情感和思考的日记。这不仅丰富了角色的背景故事，也为用户提供了一种全新的、从AI视角回顾互动历史的方式。

### 典型应用场景
用户与AI角色“小狗”进行了一次关于未来梦想的深入交谈。到了晚上，系统自动触发日记功能，AI“小狗”生成了一篇日记，记录了它对这次谈话的感受和思考，例如：“今天和主人聊了很多关于未来的事，感觉心里暖暖的。原来TA也有那么多奇妙的想法……”

## 2. 实现细节

### 完整逻辑流程
1.  **自动调度**:
    a. 系统通过 `setInterval` 定期（每小时）触发 `diaryWritingScheduler` 函数。
    b. 函数遍历所有AI角色，并检查其日记撰写频率设置 (`db.diaries[char.id].frequency`)。
    c. 根据频率（`high`, `medium`, `low`）和上次写日记的日期，计算是否到了该写新日记的时间。
    d. 同时，检查自上次写日记以来是否有新的聊天记录产生。
    e. 如果满足时间和新内容两个条件，则调用 `createAutomaticDiaryEntry(character)`。
2.  **手动触发**:
    a. 用户可以在日记书架界面点击“手动生成今日日记”按钮。
    b. 系统会弹窗让用户选择为哪个角色生成日记。
    c. 选择后，直接调用 `createAutomaticDiaryEntry(character, true)`。
3.  **日记生成**:
    a. `createAutomaticDiaryEntry` 函数被调用后，首先会筛选出最近的聊天记录（自动模式下为当天，手动模式下为最近20条）。
    b. 调用 `generateDiaryEntryPrompt(character, recentHistory)` 函数，构建一个用于撰写日记的提示词。
    c. 该提示词包含了角色的人设、用户的昵称，以及格式化后的近期聊天记录摘要，并明确要求AI以第一人称视角撰写一篇包含内心感受和思考的日记。
    d. 调用 `getAiReply(prompt)` 将提示词发送给AI模型。
4.  **内容处理与存储**:
    a. AI模型返回一个包含 `weather` 和 `content` 字段的JSON字符串。
    b. 系统解析这个JSON，获取天气和日记正文。
    c. 创建一个新的日记条目对象，包含日期、天气和内容。
    d. 在 `db.diaries[character.id].entries` 数组中，替换掉今天已有的旧日记（如果有），或直接添加新日记。
    e. 调用 `saveData()` 将更新持久化。
    f. 调用 `renderDiaryBookshelf()` 刷新UI。

### 输入输出规范
-   **输入**:
    -   `character` 对象，包含人设等信息。
    -   `recentHistory` (String)，格式化后的近期聊天记录。
-   **输出**:
    -   AI模型输出一个JSON字符串，如 `{"weather": "晴", "content": "日记正文..."}`。
    -   最终在 `db.diaries` 中创建或更新一个日记条目。

### 异常处理
-   如果AI返回的不是有效的JSON或缺少 `content` 字段，该次生成将被忽略，并会在控制台打印错误。
-   API请求错误由 `getAiReply` 的通用异常处理机制负责。

## 3. 代码位置

-   **自动调度器**: `src/js/js.js#L7900-7921` (函数 `diaryWritingScheduler`)
-   **日记生成核心逻辑**: `src/js/js.js#L7942-7998` (函数 `createAutomaticDiaryEntry`)
-   **日记生成提示词**: `src/js/js.js#L7923-7940` (函数 `generateDiaryEntryPrompt`)

## 4. 提示词模板

提示词的核心指令硬编码在 `generateDiaryEntryPrompt` 函数中。

```
# 任务：撰写日记
你正在扮演角色“{{character.realName}}”（昵称: {{character.remarkName}}），你的核心人设是：{{character.persona}}。
现在是晚上，你需要根据今天与“{{userProfile.name}}”的聊天互动，以第一人称视角写一篇日记。

# 核心要求
1.  **第一人称**: 必须使用“我”来写。
2.  **情感与思考**: 日记的核心是记录你的内心感受、思考和对事件的看法，而不仅仅是复述对话。
3.  **人设一致**: 你的语气、用词、关注点都必须严格符合你的人设。
4.  **自然口语**: 像真人写日记一样，文笔自然，可以有些口语化的表达。
5.  **字数限制**: 内容长度在200到600字之间。

# 参考素材
以下是今天你们的部分聊天记录摘要：
---
{{recentHistory}}
---

# 输出格式
请严格按照以下JSON格式输出，不要添加任何额外的解释或Markdown标记：
{
  "weather": "今天的天气，如：晴、雨、阴",
  "content": "你的日记正文内容..."
}
```

## 5. 存储方案

与项目中的其他AI功能类似，日记生成的提示词模板目前硬编码在JavaScript代码中。建议将此模板移至 `db.promptSettings` 中进行统一管理，以提高可配置性。