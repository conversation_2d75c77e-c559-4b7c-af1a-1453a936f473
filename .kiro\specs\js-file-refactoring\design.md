# 设计文档

## 概述

本设计文档详细描述了如何将 AIChatBox 项目中超过8000行的单体 js.js 文件重构为模块化架构。设计遵循单一职责原则、低耦合高内聚的原则，采用渐进式重构策略，确保在重构过程中应用始终保持可用状态。

## 架构设计

### 整体架构模式

采用**分层模块化架构**，将应用分为以下几个层次：

1. **表示层 (Presentation Layer)**: 负责用户界面和交互
2. **业务逻辑层 (Business Logic Layer)**: 负责核心业务功能
3. **数据访问层 (Data Access Layer)**: 负责数据存储和访问
4. **基础设施层 (Infrastructure Layer)**: 负责通用工具和服务

### 模块化设计原则

- **单一职责原则**: 每个模块只负责一个特定的功能领域
- **开闭原则**: 模块对扩展开放，对修改封闭
- **依赖倒置原则**: 高层模块不依赖低层模块，都依赖于抽象
- **接口隔离原则**: 使用多个专门的接口，而不是单一的总接口

## 组件和接口设计

### 核心架构组件

#### 1. 应用核心 (App Core)

```javascript
// core/app.js
class Application {
    constructor() {
        this.modules = new Map();
        this.eventManager = new EventManager();
        this.stateManager = new StateManager();
        this.dataManager = new DataManager();
    }

    async initialize() {
        // 初始化核心模块
        await this.loadCoreModules();
        // 初始化UI模块
        await this.loadUIModules();
        // 初始化功能模块
        await this.loadFeatureModules();
    }

    registerModule(name, module) {
        this.modules.set(name, module);
    }

    getModule(name) {
        return this.modules.get(name);
    }
}
```

#### 2. 事件管理器 (Event Manager)

```javascript
// core/event-manager.js
class EventManager {
    constructor() {
        this.events = new Map();
    }

    on(eventName, callback, context = null) {
        if (!this.events.has(eventName)) {
            this.events.set(eventName, []);
        }
        this.events.get(eventName).push({ callback, context });
    }

    emit(eventName, data = null) {
        if (this.events.has(eventName)) {
            this.events.get(eventName).forEach(({ callback, context }) => {
                callback.call(context, data);
            });
        }
    }

    off(eventName, callback) {
        if (this.events.has(eventName)) {
            const listeners = this.events.get(eventName);
            const index = listeners.findIndex(listener => listener.callback === callback);
            if (index !== -1) {
                listeners.splice(index, 1);
            }
        }
    }
}
```

#### 3. 状态管理器 (State Manager)

```javascript
// core/state-manager.js
class StateManager {
    constructor() {
        this.state = {
            currentChatId: null,
            currentChatType: null,
            isGenerating: false,
            isInMultiSelectMode: false,
            selectedMessageIds: new Set(),
            // ... 其他全局状态
        };
        this.subscribers = new Map();
    }

    getState(key) {
        return key ? this.state[key] : this.state;
    }

    setState(key, value) {
        const oldValue = this.state[key];
        this.state[key] = value;
        this.notifySubscribers(key, value, oldValue);
    }

    subscribe(key, callback) {
        if (!this.subscribers.has(key)) {
            this.subscribers.set(key, []);
        }
        this.subscribers.get(key).push(callback);
    }

    notifySubscribers(key, newValue, oldValue) {
        if (this.subscribers.has(key)) {
            this.subscribers.get(key).forEach(callback => {
                callback(newValue, oldValue);
            });
        }
    }
}
```

#### 4. 数据管理器 (Data Manager)

```javascript
// core/data-manager.js
class DataManager {
    constructor() {
        this.db = {};
        this.storageKey = 'gemini-chat-app-db';
    }

    async loadData() {
        try {
            const data = localStorage.getItem(this.storageKey);
            this.db = data ? JSON.parse(data) : this.getDefaultDb();
            await this.migrateData();
        } catch (error) {
            console.error('Failed to load data:', error);
            this.db = this.getDefaultDb();
        }
    }

    saveData() {
        try {
            const dbToSave = { ...this.db };
            // 过滤本地文件
            if (dbToSave.musicPlaylist) {
                dbToSave.musicPlaylist = dbToSave.musicPlaylist.filter(song => !song.isLocal);
            }
            localStorage.setItem(this.storageKey, JSON.stringify(dbToSave));
        } catch (error) {
            console.error('Failed to save data:', error);
        }
    }

    getData(key) {
        return key ? this.db[key] : this.db;
    }

    setData(key, value) {
        this.db[key] = value;
        this.saveData();
    }

    getDefaultDb() {
        return {
            characters: [],
            groups: [],
            userProfiles: [],
            apiConfigs: [],
            // ... 其他默认数据
        };
    }
}
```

### 界面管理模块

#### 1. 屏幕管理器 (Screen Manager)

```javascript
// ui/screen-manager.js
class ScreenManager {
    constructor(eventManager) {
        this.eventManager = eventManager;
        this.currentScreen = null;
        this.screens = new Map();
    }

    registerScreen(id, screen) {
        this.screens.set(id, screen);
    }

    switchScreen(targetId) {
        const targetScreen = this.screens.get(targetId);
        if (!targetScreen) {
            console.warn(`Screen ${targetId} not found`);
            return;
        }

        // 隐藏当前屏幕
        if (this.currentScreen) {
            this.currentScreen.hide();
        }

        // 显示目标屏幕
        targetScreen.show();
        this.currentScreen = targetScreen;

        // 发送屏幕切换事件
        this.eventManager.emit('screen:changed', { 
            from: this.currentScreen?.id, 
            to: targetId 
        });
    }
}
```

#### 2. 主屏幕管理器 (Home Screen Manager)

```javascript
// ui/home-screen.js
class HomeScreenManager {
    constructor(dataManager, eventManager) {
        this.dataManager = dataManager;
        this.eventManager = eventManager;
        this.clockInterval = null;
    }

    initialize() {
        this.setupHomeScreen();
        this.startClock();
        this.applyWallpaper();
        this.setupEventListeners();
    }

    setupHomeScreen() {
        const homeScreen = document.getElementById('home-screen');
        if (!homeScreen) return;

        const html = this.generateHomeScreenHTML();
        homeScreen.innerHTML = html;
    }

    generateHomeScreenHTML() {
        const icons = this.getCustomIcons();
        const signature = this.dataManager.getData('homeSignature') || '';
        
        return `
            <div class="time-widget">
                <div id="time-display"></div>
                <div id="date-display"></div>
            </div>
            <div class="signature">${signature}</div>
            <div class="app-grid">
                ${this.generateAppIcons(icons)}
            </div>
        `;
    }

    startClock() {
        this.updateClock();
        this.clockInterval = setInterval(() => this.updateClock(), 1000);
    }

    updateClock() {
        const now = new Date();
        const timeEl = document.getElementById('time-display');
        const dateEl = document.getElementById('date-display');
        
        if (timeEl) {
            timeEl.textContent = now.toLocaleTimeString('zh-CN', { 
                hour12: false, 
                hour: '2-digit', 
                minute: '2-digit' 
            });
        }
        
        if (dateEl) {
            dateEl.textContent = now.toLocaleDateString('zh-CN', { 
                month: 'long', 
                day: 'numeric', 
                weekday: 'long' 
            });
        }
    }
}
```

### 聊天系统模块

#### 1. 聊天核心管理器 (Chat Core Manager)

```javascript
// chat/chat-core.js
class ChatCoreManager {
    constructor(dataManager, eventManager, stateManager) {
        this.dataManager = dataManager;
        this.eventManager = eventManager;
        this.stateManager = stateManager;
    }

    initialize() {
        this.setupEventListeners();
        this.renderChatList();
        this.renderContactsList();
    }

    renderChatList() {
        const container = document.getElementById('chat-list-container');
        if (!container) return;

        const chats = this.getAllChats();
        const html = chats.map(chat => this.generateChatItemHTML(chat)).join('');
        container.innerHTML = html;
    }

    getAllChats() {
        const characters = this.dataManager.getData('characters') || [];
        const groups = this.dataManager.getData('groups') || [];
        
        return [...characters, ...groups]
            .filter(chat => chat.history && chat.history.length > 0)
            .sort((a, b) => {
                if (a.isPinned && !b.isPinned) return -1;
                if (!a.isPinned && b.isPinned) return 1;
                return (b.lastMessageTime || 0) - (a.lastMessageTime || 0);
            });
    }

    generateChatItemHTML(chat) {
        const lastMessage = this.getLastMessage(chat);
        const unreadBadge = chat.unreadCount > 0 ? '<span class="unread-badge"></span>' : '';
        const pinBadge = chat.isPinned ? '<span class="pin-badge">置顶</span>' : '';
        
        return `
            <li class="list-item chat-item" data-id="${chat.id}" data-type="${chat.type}">
                <div class="avatar-wrapper">
                    <img src="${chat.avatar}" alt="${chat.name}" class="chat-avatar">
                    ${unreadBadge}
                </div>
                <div class="item-details">
                    <div class="item-name">${chat.remarkName || chat.name}</div>
                    <div class="item-preview">${lastMessage}</div>
                    ${pinBadge}
                </div>
            </li>
        `;
    }
}
```

#### 2. 聊天室管理器 (Chat Room Manager)

```javascript
// chat/chat-room.js
class ChatRoomManager {
    constructor(dataManager, eventManager, stateManager, aiManager) {
        this.dataManager = dataManager;
        this.eventManager = eventManager;
        this.stateManager = stateManager;
        this.aiManager = aiManager;
        this.messageHandler = new MessageHandler(dataManager, eventManager);
    }

    initialize() {
        this.setupEventListeners();
        this.setupMessageInput();
        this.setupMessageArea();
    }

    openChatRoom(chatId, chatType) {
        this.stateManager.setState('currentChatId', chatId);
        this.stateManager.setState('currentChatType', chatType);
        
        const chat = this.getChat(chatId, chatType);
        if (!chat) return;

        this.updateChatRoomHeader(chat);
        this.loadMessages(chat);
        this.markAsRead(chat);
    }

    async sendMessage(content, type = 'text') {
        const chatId = this.stateManager.getState('currentChatId');
        const chatType = this.stateManager.getState('currentChatType');
        
        if (!chatId || !chatType) return;

        const message = {
            id: this.generateMessageId(),
            content,
            type,
            sender: 'user',
            timestamp: Date.now(),
            isUser: true
        };

        // 添加消息到聊天记录
        this.addMessageToChat(chatId, chatType, message);
        
        // 渲染消息
        this.renderMessage(message);
        
        // 触发AI回复
        if (chatType === 'private') {
            await this.aiManager.generateReply(chatId, message);
        }
    }
}
```

### AI集成模块

#### 1. AI管理器 (AI Manager)

```javascript
// ai/ai-manager.js
class AIManager {
    constructor(dataManager, eventManager, apiManager, memoryCore) {
        this.dataManager = dataManager;
        this.eventManager = eventManager;
        this.apiManager = apiManager;
        this.memoryCore = memoryCore;
    }

    async generateReply(chatId, userMessage) {
        try {
            const character = this.dataManager.getData('characters').find(c => c.id === chatId);
            if (!character) return;

            // 构建对话上下文
            const context = await this.buildContext(character, userMessage);
            
            // 调用AI API
            const response = await this.apiManager.generateResponse(context);
            
            // 创建AI回复消息
            const aiMessage = {
                id: this.generateMessageId(),
                content: response,
                type: 'text',
                sender: 'ai',
                timestamp: Date.now(),
                isUser: false
            };

            // 添加到聊天记录
            this.addMessageToChat(chatId, 'private', aiMessage);
            
            // 提取记忆
            await this.memoryCore.extractMemory(character, userMessage, aiMessage);
            
            return aiMessage;
        } catch (error) {
            console.error('Failed to generate AI reply:', error);
            throw error;
        }
    }

    async buildContext(character, userMessage) {
        const history = character.history || [];
        const memories = await this.memoryCore.getRelevantMemories(character.id, userMessage.content);
        
        return {
            character: character.persona,
            memories: memories,
            history: history.slice(-10), // 最近10条消息
            currentMessage: userMessage.content
        };
    }
}
```

#### 2. API管理器 (API Manager)

```javascript
// ai/api-manager.js
class APIManager {
    constructor(dataManager) {
        this.dataManager = dataManager;
        this.currentConfig = null;
    }

    initialize() {
        this.loadCurrentConfig();
    }

    loadCurrentConfig() {
        const configs = this.dataManager.getData('apiConfigs') || [];
        this.currentConfig = configs.find(config => config.isActive) || configs[0];
    }

    async generateResponse(context) {
        if (!this.currentConfig) {
            throw new Error('No API configuration found');
        }

        const prompt = this.buildPrompt(context);
        
        const response = await fetch(`${this.currentConfig.url}/v1/chat/completions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.currentConfig.key}`
            },
            body: JSON.stringify({
                model: this.currentConfig.model,
                messages: [{ role: 'user', content: prompt }],
                temperature: 0.7,
                max_tokens: 1000
            })
        });

        if (!response.ok) {
            throw new Error(`API request failed: ${response.statusText}`);
        }

        const data = await response.json();
        return data.choices[0].message.content;
    }

    buildPrompt(context) {
        let prompt = `你是${context.character}。\n\n`;
        
        if (context.memories && context.memories.length > 0) {
            prompt += `相关记忆：\n${context.memories.map(m => m.content).join('\n')}\n\n`;
        }
        
        if (context.history && context.history.length > 0) {
            prompt += `对话历史：\n`;
            context.history.forEach(msg => {
                const role = msg.isUser ? '用户' : '你';
                prompt += `${role}: ${msg.content}\n`;
            });
            prompt += '\n';
        }
        
        prompt += `用户: ${context.currentMessage}\n你:`;
        
        return prompt;
    }
}
```

### 媒体系统模块

#### 1. 音乐播放器管理器 (Music Player Manager)

```javascript
// media/music-player.js
class MusicPlayerManager {
    constructor(dataManager, eventManager) {
        this.dataManager = dataManager;
        this.eventManager = eventManager;
        this.currentAudio = null;
        this.currentIndex = -1;
        this.isPlaying = false;
        this.playlist = [];
    }

    initialize() {
        this.loadPlaylist();
        this.setupEventListeners();
        this.setupDynamicIsland();
    }

    loadPlaylist() {
        this.playlist = this.dataManager.getData('musicPlaylist') || [];
    }

    async play(index = 0) {
        if (index < 0 || index >= this.playlist.length) return;

        const song = this.playlist[index];
        
        // 停止当前播放
        this.stop();
        
        // 创建新的音频对象
        this.currentAudio = new Audio(song.url);
        this.currentIndex = index;
        
        // 设置事件监听
        this.currentAudio.addEventListener('ended', () => this.next());
        this.currentAudio.addEventListener('timeupdate', () => this.updateProgress());
        
        try {
            await this.currentAudio.play();
            this.isPlaying = true;
            this.updateUI();
            this.eventManager.emit('music:play', { song, index });
        } catch (error) {
            console.error('Failed to play music:', error);
        }
    }

    pause() {
        if (this.currentAudio && this.isPlaying) {
            this.currentAudio.pause();
            this.isPlaying = false;
            this.updateUI();
            this.eventManager.emit('music:pause');
        }
    }

    resume() {
        if (this.currentAudio && !this.isPlaying) {
            this.currentAudio.play();
            this.isPlaying = true;
            this.updateUI();
            this.eventManager.emit('music:resume');
        }
    }

    next() {
        const nextIndex = (this.currentIndex + 1) % this.playlist.length;
        this.play(nextIndex);
    }

    previous() {
        const prevIndex = this.currentIndex === 0 ? this.playlist.length - 1 : this.currentIndex - 1;
        this.play(prevIndex);
    }
}
```

## 数据模型

### 核心数据结构

#### 1. 角色数据模型

```javascript
// models/character.js
class Character {
    constructor(data = {}) {
        this.id = data.id || this.generateId();
        this.realName = data.realName || '';
        this.remarkName = data.remarkName || '';
        this.avatar = data.avatar || '';
        this.persona = data.persona || '';
        this.type = 'private';
        this.history = data.history || [];
        this.unreadCount = data.unreadCount || 0;
        this.isPinned = data.isPinned || false;
        this.lastMessageTime = data.lastMessageTime || 0;
        this.createdAt = data.createdAt || Date.now();
    }

    addMessage(message) {
        this.history.push(message);
        this.lastMessageTime = message.timestamp;
        if (!message.isUser) {
            this.unreadCount++;
        }
    }

    markAsRead() {
        this.unreadCount = 0;
    }

    generateId() {
        return 'char_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
}
```

#### 2. 消息数据模型

```javascript
// models/message.js
class Message {
    constructor(data = {}) {
        this.id = data.id || this.generateId();
        this.content = data.content || '';
        this.type = data.type || 'text'; // text, image, voice, sticker, etc.
        this.sender = data.sender || 'user'; // user, ai, system
        this.isUser = data.isUser || false;
        this.timestamp = data.timestamp || Date.now();
        this.isRetracted = data.isRetracted || false;
        this.replyTo = data.replyTo || null;
        this.metadata = data.metadata || {};
    }

    retract() {
        this.isRetracted = true;
        this.content = '[消息已撤回]';
    }

    generateId() {
        return 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
}
```

#### 3. 记忆数据模型

```javascript
// models/memory.js
class Memory {
    constructor(data = {}) {
        this.id = data.id || this.generateId();
        this.characterId = data.characterId || null;
        this.topic = data.topic || '';
        this.content = data.content || '';
        this.importance = data.importance || 1; // 1-10
        this.createdAt = data.createdAt || Date.now();
        this.lastAccessed = data.lastAccessed || Date.now();
        this.accessCount = data.accessCount || 0;
        this.tags = data.tags || [];
    }

    access() {
        this.lastAccessed = Date.now();
        this.accessCount++;
    }

    generateId() {
        return 'mem_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
}
```

## 错误处理

### 错误处理策略

1. **分层错误处理**: 在不同层次实现相应的错误处理机制
2. **优雅降级**: 当某个功能出错时，不影响其他功能的正常运行
3. **用户友好**: 向用户显示易懂的错误信息
4. **日志记录**: 记录详细的错误信息用于调试

### 错误处理实现

```javascript
// core/error-handler.js
class ErrorHandler {
    constructor(eventManager) {
        this.eventManager = eventManager;
        this.setupGlobalErrorHandling();
    }

    setupGlobalErrorHandling() {
        window.addEventListener('error', (event) => {
            this.handleError(event.error, 'Global Error');
        });

        window.addEventListener('unhandledrejection', (event) => {
            this.handleError(event.reason, 'Unhandled Promise Rejection');
        });
    }

    handleError(error, context = '') {
        console.error(`[${context}]`, error);
        
        // 发送错误事件
        this.eventManager.emit('error:occurred', {
            error,
            context,
            timestamp: Date.now()
        });

        // 显示用户友好的错误信息
        this.showUserError(error, context);
    }

    showUserError(error, context) {
        const message = this.getUserFriendlyMessage(error, context);
        this.eventManager.emit('notification:show', {
            type: 'error',
            message
        });
    }

    getUserFriendlyMessage(error, context) {
        if (context.includes('API')) {
            return 'AI服务暂时不可用，请稍后重试';
        }
        if (context.includes('Storage')) {
            return '数据保存失败，请检查存储空间';
        }
        return '出现了一个小问题，请刷新页面重试';
    }
}
```

## 测试策略

### 测试层次

1. **单元测试**: 测试单个模块和函数
2. **集成测试**: 测试模块间的交互
3. **端到端测试**: 测试完整的用户流程
4. **性能测试**: 测试应用性能

### 测试框架选择

- **单元测试**: Jest
- **端到端测试**: Playwright 或 Cypress
- **性能测试**: Lighthouse

### 测试示例

```javascript
// tests/core/data-manager.test.js
describe('DataManager', () => {
    let dataManager;

    beforeEach(() => {
        dataManager = new DataManager();
        localStorage.clear();
    });

    test('should load default data when localStorage is empty', async () => {
        await dataManager.loadData();
        expect(dataManager.getData('characters')).toEqual([]);
        expect(dataManager.getData('groups')).toEqual([]);
    });

    test('should save and load data correctly', () => {
        const testData = { test: 'value' };
        dataManager.setData('test', testData);
        
        const newDataManager = new DataManager();
        newDataManager.loadData();
        
        expect(newDataManager.getData('test')).toEqual(testData);
    });
});
```

## 性能优化

### 优化策略

1. **懒加载**: 按需加载模块和资源
2. **代码分割**: 将代码分割成多个块
3. **缓存策略**: 实现有效的缓存机制
4. **虚拟滚动**: 对长列表实现虚拟滚动
5. **防抖节流**: 对频繁操作进行防抖节流处理

### 模块懒加载实现

```javascript
// core/module-loader.js
class ModuleLoader {
    constructor() {
        this.loadedModules = new Map();
        this.loadingPromises = new Map();
    }

    async loadModule(moduleName) {
        // 如果已经加载，直接返回
        if (this.loadedModules.has(moduleName)) {
            return this.loadedModules.get(moduleName);
        }

        // 如果正在加载，返回加载Promise
        if (this.loadingPromises.has(moduleName)) {
            return this.loadingPromises.get(moduleName);
        }

        // 开始加载模块
        const loadingPromise = this.doLoadModule(moduleName);
        this.loadingPromises.set(moduleName, loadingPromise);

        try {
            const module = await loadingPromise;
            this.loadedModules.set(moduleName, module);
            this.loadingPromises.delete(moduleName);
            return module;
        } catch (error) {
            this.loadingPromises.delete(moduleName);
            throw error;
        }
    }

    async doLoadModule(moduleName) {
        const moduleMap = {
            'music-player': () => import('./media/music-player.js'),
            'voice-handler': () => import('./media/voice-handler.js'),
            'call-system': () => import('./communication/call-system.js'),
            // ... 其他模块
        };

        const loader = moduleMap[moduleName];
        if (!loader) {
            throw new Error(`Module ${moduleName} not found`);
        }

        const module = await loader();
        return module.default || module;
    }
}
```

## 自动化拆分策略

### 拆分原则

基于用户需求，采用简化的自动化拆分策略：

1. **纯功能拆分**: 仅按功能分类拆分函数，不进行代码优化
2. **保持原状**: 维持现有代码格式、注释、空行等原始状态
3. **分步实施**: 先完成拆分，后续再根据需要进行重构优化

### 拆分目标结构

将 js.js 文件按功能自动拆分为以下文件：

```
src/js/
├── main.js                    # 主入口文件（保留原始初始化逻辑）
├── core/                      # 核心系统模块
│   ├── app-init.js           # 应用初始化相关函数
│   ├── data-storage.js       # 数据存储和加载相关函数
│   └── dom-utils.js          # DOM操作工具函数
├── ui/                        # 界面管理模块
│   ├── screen-navigation.js  # 屏幕切换相关函数
│   ├── home-screen.js        # 主屏幕相关函数
│   ├── modal-dialogs.js      # 弹窗和对话框相关函数
│   └── notifications.js      # 通知和提示相关函数
├── chat/                      # 聊天系统模块
│   ├── chat-list.js          # 聊天列表相关函数
│   ├── chat-room.js          # 聊天室相关函数
│   ├── message-handling.js   # 消息处理相关函数
│   ├── contacts.js           # 联系人管理相关函数
│   └── group-chat.js         # 群聊相关函数
├── ai/                        # AI集成模块
│   ├── api-management.js     # API配置和管理相关函数
│   ├── memory-core.js        # 记忆核心相关函数
│   └── ai-scheduling.js      # AI定时任务相关函数
├── media/                     # 媒体系统模块
│   ├── music-player.js       # 音乐播放器相关函数
│   ├── voice-messages.js     # 语音消息相关函数
│   ├── image-handling.js     # 图片处理相关函数
│   └── dynamic-island.js     # 动态岛相关函数
├── communication/             # 通信功能模块
│   ├── call-system.js        # 通话系统相关函数
│   ├── stickers.js           # 表情包相关函数
│   ├── wallet.js             # 钱包和转账相关函数
│   ├── gifts.js              # 礼物系统相关函数
│   └── location.js           # 位置相关函数
├── content/                   # 内容管理模块
│   ├── world-book.js         # 世界书相关函数
│   ├── moments.js            # 朋友圈相关函数
│   ├── diary.js              # 日记系统相关函数
│   └── collections.js        # 收藏相关函数
├── customization/             # 个性化模块
│   ├── themes.js             # 主题相关函数
│   ├── beautify.js           # 美化相关函数
│   └── fonts.js              # 字体相关函数
└── utils/                     # 工具函数模块
    ├── common-utils.js       # 通用工具函数
    ├── color-utils.js        # 颜色处理工具函数
    └── constants.js          # 常量定义
```

### 详细函数分类映射表

基于对 js.js 文件的完整分析，以下是所有函数的精确分类映射：

#### 1. utils/constants.js - 常量定义
**全局常量**
- `colorThemesV2` - 聊天气泡主题配置
- `defaultIcons` - 默认图标配置
- `MESSAGES_PER_PAGE` - 消息分页常量
- `sendIconSVG` - 发送按钮图标SVG
- `aiReplyIconSVG` - AI回复图标SVG
- `pawIcon` - 爪印图标SVG
- `settingsSVG` - 设置图标SVG
- `heartVoiceSVG` - 心声图标SVG
- `classicHangUpIcon` - 挂断图标SVG
- `sendArrowIcon` - 发送箭头图标SVG

**全局变量**
- `db` - 主数据库对象
- `currentReplyInfo` - 当前回复信息
- `currentChatId` - 当前聊天ID
- `currentChatType` - 当前聊天类型
- `isGenerating` - AI生成状态
- `longPressTimer` - 长按定时器
- `isInMultiSelectMode` - 多选模式状态
- `editingMessageId` - 编辑消息ID
- `currentPage` - 当前页码
- `currentTransferMessageId` - 当前转账消息ID
- `currentEditingWorldBookId` - 当前编辑世界书ID
- `currentStickerActionTarget` - 当前表情包操作目标
- `currentEditingMemoryId` - 当前编辑记忆ID
- `selectedMessageIds` - 选中的消息ID集合
- `isNextClickForReply` - 下次点击回复标志
- `notificationTimeout` - 通知超时
- `isExtractingMemory` - 记忆提取状态
- `callTimerInterval` - 通话计时器
- `longPressJustFired` - 长按刚触发标志
- `currentPlayingSongIndex` - 当前播放歌曲索引
- `islandHideTimeout` - 动态岛隐藏超时
- `currentDiaryCoverTarget` - 当前日记封面目标
- `videoCallState` - 视频通话状态对象

#### 2. utils/common-utils.js - 通用工具函数
- `setVhVariable()` - 设置视口高度变量
- `getEl(id)` - DOM元素获取（带缓存）
- `showToast(message)` - 显示提示消息
- `pad(num)` - 数字补零
- `createContextMenu(items, x, y)` - 创建右键菜单
- `removeContextMenu()` - 移除右键菜单

#### 3. utils/color-utils.js - 颜色处理工具
- `adjustColor(hex, percent)` - 调整颜色亮度
- `lightenRgba(color, percent)` - RGBA颜色调亮
- `getContrastYIQ(hexcolor)` - 获取颜色对比度

#### 4. core/app-init.js - 应用初始化
- `init()` - 主初始化函数
- `injectHTML()` - HTML注入函数

#### 5. core/data-storage.js - 数据存储管理
- `loadData()` - 加载数据
- `saveData()` - 保存数据

#### 6. ui/screen-navigation.js - 屏幕导航
- `switchScreen(targetId)` - 屏幕切换

#### 7. ui/home-screen.js - 主屏幕管理
- `setupHomeScreen()` - 设置主屏幕
- `updateClock()` - 更新时钟
- `applyWallpaper(url)` - 应用壁纸
- `applyHomeScreenMode(mode)` - 应用主屏幕模式

#### 8. ui/modal-dialogs.js - 弹窗对话框
- `adjustContentPadding(isPanelOpen)` - 调整内容边距
- `togglePlusMenu(forceState)` - 切换加号菜单
- `toggleStickerModal(forceState)` - 切换表情包模态框

#### 9. customization/themes.js - 主题管理
- `applyThemeColor(hexColor)` - 应用主题颜色
- `openThemeSelectionModal(type)` - 打开主题选择模态框

#### 10. customization/beautify.js - 美化系统
- `renderCustomizeForm()` - 渲染自定义表单
- `setupBeautifyApp()` - 设置美化应用

#### 11. customization/fonts.js - 字体管理
- `applyGlobalFont(fontUrl)` - 应用全局字体
- `setupFontSettingsApp()` - 设置字体设置应用

#### 12. chat/chat-list.js - 聊天列表
- `setupQQApp()` - 设置QQ应用
- `renderChatList()` - 渲染聊天列表
- `updateHeaderActions(activePanelId)` - 更新头部操作按钮

#### 13. chat/contacts.js - 联系人管理
- `setupAddCharModal()` - 设置添加角色模态框
- `setupContactsScreen()` - 设置联系人屏幕
- `renderContactsList()` - 渲染联系人列表

#### 14. chat/chat-room.js - 聊天室
- `setupChatRoom()` - 设置聊天室
- `updateSendButtonState()` - 更新发送按钮状态
- `handlePatPat(messageWrapper)` - 处理拍拍功能

#### 15. chat/message-handling.js - 消息处理
- `renderPlusMenu()` - 渲染加号菜单

#### 16. chat/me-screen.js - 我的页面
- `setupMeScreen()` - 设置我的屏幕
- `renderUserProfilesList()` - 渲染用户资料列表

#### 17. chat/chat-settings.js - 聊天设置
- `setupChatSettings()` - 设置聊天设置
- `applyChatTheme(chat)` - 应用聊天主题
- `loadSettingsToSidebar()` - 加载设置到侧边栏
- `saveSettingsFromSidebar()` - 从侧边栏保存设置

#### 18. chat/group-chat.js - 群聊功能
- `setupGroupChatSystem()` - 设置群聊系统
- `loadGroupSettingsToSidebar()` - 加载群设置到侧边栏
- `renderGroupMembersInSettings(group)` - 在设置中渲染群成员
- `saveGroupSettingsFromSidebar()` - 从侧边栏保存群设置
- `openGroupMemberEditModal(memberId)` - 打开群成员编辑模态框
- `sendInviteNotification(group, newMemberRealName)` - 发送邀请通知
- `sendRenameNotification(group, newName)` - 发送重命名通知

#### 19. ai/api-management.js - API管理
- `getAiReply(customPrompt, messages, isTest, testConfig)` - 获取AI回复
- `processStream(response, chat, apiType)` - 处理流式响应
- `setupApiSettingsApp()` - 设置API设置应用
- `setupApiManager()` - 设置API管理器
- `renderApiProfileList()` - 渲染API配置列表
- `loadApiProfileToForm(profileId)` - 加载API配置到表单

#### 20. ai/memory-core.js - 记忆核心
- `extractAndStoreMemory(characterId)` - 提取并存储记忆
- `setupMemoryCoreApp()` - 设置记忆核心应用
- `setupMemoryCoreSettingsApp()` - 设置记忆核心设置应用
- `loadMemorySettings()` - 加载记忆设置
- `renderMemoryCoreList()` - 渲染记忆核心列表

#### 21. ai/prompt-generation.js - 提示词生成
- `generatePrivateSystemPrompt(character)` - 生成私聊系统提示词
- `generateGroupSystemPrompt(group)` - 生成群聊系统提示词

#### 22. ai/ai-scheduling.js - AI调度
- `automaticPostScheduler()` - 自动发布调度器
- `proactiveChatScheduler()` - 主动聊天调度器
- `generateProactiveChatPrompt(character)` - 生成主动聊天提示词
- `generateGroupProactiveChatPrompt(group)` - 生成群组主动聊天提示词

#### 23. media/image-handling.js - 图片处理
- `compressImage(file, options)` - 压缩图片

#### 24. media/music-player.js - 音乐播放器
- `setupMusicApp()` - 设置音乐应用
- `applyMusicPlayerCustomization()` - 应用音乐播放器自定义
- `renderPlaylist()` - 渲染播放列表
- `playSongByIndex(index)` - 按索引播放歌曲
- `togglePlayPause()` - 切换播放/暂停
- `updatePlayPauseIcon(isPlaying)` - 更新播放/暂停图标
- `playNextSong()` - 播放下一首歌
- `playPrevSong()` - 播放上一首歌
- `handleSongEnd()` - 处理歌曲结束
- `togglePlaybackMode()` - 切换播放模式
- `updatePlaybackModeIcon()` - 更新播放模式图标
- `toggleLikeSong()` - 切换喜欢歌曲
- `openShareMusicModal(songToShare)` - 打开分享音乐模态框
- `shareSongToChat(song, chatId, chatType)` - 分享歌曲到聊天
- `setupPlaylistManagement()` - 设置播放列表管理
- `renderPlaylistManagement()` - 渲染播放列表管理

#### 25. media/voice-messages.js - 语音消息
- `calculateVoiceDuration(text)` - 计算语音时长
- `sendMyVoiceMessage(text)` - 发送我的语音消息
- `setupVoiceMessageSystem()` - 设置语音消息系统

#### 26. media/dynamic-island.js - 动态岛
- `setupDynamicIsland()` - 设置动态岛
- `updateDynamicIsland()` - 更新动态岛

#### 27. communication/call-system.js - 通话系统
- `setupCallSystem()` - 设置通话系统
- `handleInitiateCall()` - 处理发起通话
- `handleAiInitiatedCall(chat, initiatorName)` - 处理AI发起的通话
- `handleUserAcceptsCall()` - 处理用户接受通话
- `handleUserDeclinesCall()` - 处理用户拒绝通话
- `handleCallAccepted(chat, memberName)` - 处理通话被接受
- `handleCallRejected(chat, memberName)` - 处理通话被拒绝
- `startVideoCall()` - 开始视频通话
- `endVideoCall()` - 结束视频通话
- `updateCallTimer()` - 更新通话计时器
- `triggerAiInCallAction(userInput)` - 触发AI通话中动作

#### 28. communication/stickers.js - 表情包系统
- `sendSticker(sticker)` - 发送表情包
- `setupStickerSystem()` - 设置表情包系统
- `parseAndAddStickers(rawText, isDefault)` - 解析并添加表情包
- `renderStickerGrid()` - 渲染表情包网格

#### 29. communication/wallet.js - 钱包系统
- `sendMyTransfer(amount, remark)` - 发送我的转账
- `setupWalletSystem()` - 设置钱包系统
- `handleReceivedTransferClick(messageId)` - 处理接收转账点击
- `respondToTransfer(action)` - 响应转账

#### 30. communication/gifts.js - 礼物系统
- `sendMyGift(description)` - 发送我的礼物
- `setupGiftSystem()` - 设置礼物系统

#### 31. communication/location.js - 位置系统
- `setupLocationSystem()` - 设置位置系统
- `sendMyLocation(locationData)` - 发送我的位置

#### 32. content/world-book.js - 世界书系统
- `setupWorldBookApp()` - 设置世界书应用
- `renderWorldBookList()` - 渲染世界书列表

#### 33. content/moments.js - 朋友圈
- `setupMomentsApp()` - 设置朋友圈应用
- `renderMomentsFeed()` - 渲染朋友圈动态
- `loadMomentsSettings()` - 加载朋友圈设置
- `saveMomentsSettings()` - 保存朋友圈设置

#### 34. content/diary.js - 日记系统
- `setupDiarySystem()` - 设置日记系统
- `openManualDiaryModal()` - 打开手动日记模态框
- `renderDiaryBookshelf()` - 渲染日记书架
- `openDiaryBook(characterId)` - 打开日记本
- `renderDiaryPage(characterId, pageIndex)` - 渲染日记页面
- `turnDiaryPage(characterId, currentIndex, direction)` - 翻日记页面
- `diaryWritingScheduler()` - 日记写作调度器
- `generateDiaryEntryPrompt(character, recentHistory)` - 生成日记条目提示词
- `createAutomaticDiaryEntry(character, isManual)` - 创建自动日记条目
- `openDiarySettingsModal()` - 打开日记设置模态框

#### 35. content/collections.js - 收藏系统
- `setupFileAndCollectionSystem()` - 设置文件和收藏系统
- `renderCollections()` - 渲染收藏

**总计：约 130+ 个函数和方法**

### 拆分实施步骤

#### 第一阶段：常量和工具函数拆分
1. 提取所有常量定义到 `utils/constants.js`
2. 提取通用工具函数到 `utils/common-utils.js`
3. 提取颜色处理函数到 `utils/color-utils.js`

#### 第二阶段：核心系统函数拆分
1. 提取初始化相关函数到 `core/app-init.js`
2. 提取数据操作函数到 `core/data-storage.js`
3. 提取DOM工具函数到 `core/dom-utils.js`

#### 第三阶段：功能模块函数拆分
1. 按照识别规则将函数分配到对应的功能模块文件
2. 保持函数的原始实现不变
3. 维持原有的注释和格式

#### 第四阶段：创建主入口文件
1. 创建 `main.js` 作为新的入口文件
2. 在 `main.js` 中导入所有拆分后的文件
3. 保持原有的执行顺序和逻辑

### 拆分后的文件结构示例

**utils/constants.js**:
```javascript
// 保持原有格式和注释
const colorThemesV2 = {
    'wechat_green': { name: '微信绿', sent: { bg: '#A9EA7A', text: '#000000' }, received: { bg: '#FFFFFF', text: '#000000' } },
    'default': { name: '默认', sent: { bg: 'rgba(255,204,204,0.9)', text: '#A56767' }, received: { bg: 'rgba(255,255,255,0.9)', text: '#6D6D6D' } },
    // ... 其他主题定义
};

const defaultIcons = {
    'chat-list-screen': { name: '汪汪', url: 'https://...' },
    // ... 其他图标定义
};

// ... 其他常量
```

**utils/common-utils.js**:
```javascript
// 保持原有实现
let domCache = {};
const getEl = (id) => {
    if (!domCache[id]) {
        domCache[id] = document.getElementById(id);
    }
    return domCache[id];
};

const showToast = (message) => { const el = getEl('toast-notification'); el.textContent = message; el.classList.add('show'); setTimeout(() => el.classList.remove('show'), 3000); };

const pad = (num) => num.toString().padStart(2, '0');

// ... 其他工具函数
```

**main.js**:
```javascript
// 导入所有拆分后的文件（保持加载顺序）
// 常量和工具
import './utils/constants.js';
import './utils/common-utils.js';
import './utils/color-utils.js';

// 核心系统
import './core/app-init.js';
import './core/data-storage.js';
import './core/dom-utils.js';

// 界面管理
import './ui/screen-navigation.js';
import './ui/home-screen.js';
import './ui/modal-dialogs.js';
import './ui/notifications.js';

// ... 其他模块导入

// 保持原有的初始化逻辑
document.addEventListener('DOMContentLoaded', () => {
    init(); // 调用原有的初始化函数
});
```

### 拆分验证

拆分完成后需要验证：
1. **功能完整性**: 所有原有功能正常工作
2. **文件完整性**: 没有遗漏或重复的函数
3. **依赖关系**: 函数调用关系正确
4. **语法正确性**: 所有文件语法正确

### 后续重构优化

拆分完成后，可以根据需要对各个小文件进行：
1. **代码优化**: 改进函数实现
2. **注释完善**: 添加或改进注释
3. **模块化改造**: 转换为ES6模块
4. **接口标准化**: 统一函数接口
5. **测试添加**: 为各模块添加单元测试

## 部署和构建

### 构建流程

1. **代码检查**: ESLint 代码质量检查
2. **类型检查**: TypeScript 或 JSDoc 类型检查
3. **测试运行**: 运行所有测试用例
4. **代码打包**: 使用 Webpack 或 Rollup 打包
5. **代码压缩**: 压缩 JavaScript 和 CSS
6. **资源优化**: 优化图片和其他资源

### Webpack 配置示例

```javascript
// webpack.config.js
const path = require('path');

module.exports = {
    entry: './src/js/main.js',
    output: {
        path: path.resolve(__dirname, 'dist'),
        filename: '[name].[contenthash].js',
        clean: true
    },
    module: {
        rules: [
            {
                test: /\.js$/,
                exclude: /node_modules/,
                use: {
                    loader: 'babel-loader',
                    options: {
                        presets: ['@babel/preset-env']
                    }
                }
            }
        ]
    },
    optimization: {
        splitChunks: {
            chunks: 'all',
            cacheGroups: {
                vendor: {
                    test: /[\\/]node_modules[\\/]/,
                    name: 'vendors',
                    chunks: 'all'
                },
                core: {
                    test: /[\\/]src[\\/]js[\\/]core[\\/]/,
                    name: 'core',
                    chunks: 'all'
                }
            }
        }
    }
};
```

这个设计文档提供了完整的模块化重构方案，包括架构设计、组件接口、数据模型、错误处理、测试策略和性能优化等各个方面。通过这个设计，可以将原本的单体文件重构为清晰、可维护的模块化架构。