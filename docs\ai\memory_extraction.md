# AI功能文档：记忆提取

## 1. 功能概述

### 核心目标
自动从用户与AI角色的对话中提炼关键信息，并将其作为长期记忆存储。这使得AI角色能够在未来的互动中“记住”过去的对话细节，从而提供更具连续性和个性化的交流体验。

### 典型应用场景
当用户与AI角色的对话达到一定长度（由`autoMemory.frequency`设置决定）后，系统会自动触发此功能。例如，在用户提及自己的生日、爱好或重要经历后，该功能会捕捉这些信息并存入角色的记忆库。

## 2. 实现细节

### 完整逻辑流程
1.  当满足触发条件时，系统调用 `extractAndStoreMemory(characterId)` 函数。
2.  函数获取角色的完整数据、用户资料以及现有的角色记忆。
3.  将最近的聊天记录格式化为纯文本字符串。
4.  使用存储在 `db.memorySettings.extractionPrompt` 中的提示词模板，构建一个用于记忆提取的完整提示词。该提示词包含了聊天历史、现有记忆、用户名称和角色名称。
5.  调用核心AI接口 `getAiReply(extractionPrompt)`，将构建好的提示词发送给大语言模型。
6.  AI模型返回一段精炼后的新记忆要点（文本格式）。
7.  系统检查返回内容，如果内容有效且不包含“无需更新”等关键词，则将新记忆追加到该角色的记忆条目（`db.memoryEntries`）中。
8.  调用 `saveData()` 将更新后的数据持久化到 `localStorage`。

### 输入输出规范
-   **输入**:
    -   `characterId` (String): 目标AI角色的唯一标识符。
    -   函数内部会隐式使用 `db.characters[characterId].history` (聊天记录) 和 `db.memoryEntries` (现有记忆)。
-   **输出**:
    -   函数没有直接返回值。
    -   其副作用是更新 `db.memoryEntries` 对象中对应角色的记忆内容。
    -   AI模型的输出是一段描述新记忆的文本字符串。

### 异常处理
-   如果AI未能返回有效的记忆内容（例如，返回为空或出错），函数会抛出一个错误。
-   该错误会被捕获，并通过 `showToast('AI未能返回有效的记忆内容。')` 向用户显示一个提示。

## 3. 代码位置

-   **核心功能实现**: `src/js/js.js#L3055-3112` (函数 `extractAndStoreMemory`)
-   **AI调用点**: `src/js/js.js#L3092` (在 `extractAndStoreMemory` 函数内部调用 `getAiReply`)

## 4. 提示词模板

提示词通过一个存储在 `localStorage` 的 `db.memorySettings.extractionPrompt` 键中的基础模板动态生成。这允许用户自定义记忆提取的行为。以下是该模板的典型结构和内容：

### 记忆提取提示词模板

```
你是一个记忆提取助手。你的任务是分析用户“{{user}}”与AI角色“{{charIfNotGroup}}”之间的最新对话，并将其与现有的记忆进行对比。

请只提炼出那些关于角色性格、背景、重要事件、双方关系变化等全新的、值得长期记住的关键信息。

如果对话中没有出现任何新的、值得记录的信息，请只回复“无需更新”。

--- 现有记忆 ---
{{memories}}

--- 最新对话记录 ---
{{history}}

---
请根据以上“最新对话记录”，总结出新的记忆要点。请以简洁的列表形式呈现，不要重复“现有记忆”中已有的内容。
```

### 模板参数详解
-   `{{history}}`: 被格式化后的近期聊天记录，通常是 `用户名: 消息内容` 的格式。
-   `{{memories}}`: 该角色已有的记忆内容，作为AI判断信息是否为“新的”的依据。
-   `{{user}}`: 当前用户的昵称。
-   `{{charIfNotGroup}}`: AI角色的真实姓名。

## 5. 存储方案

### 当前方案
目前，记忆提取功能的提示词模板已经通过 `db.memorySettings.extractionPrompt` 存储在 `localStorage` 中，这是一个良好的实践，因为它将提示词与业务逻辑代码解耦，方便进行动态修改和管理。

### 实施建议
无需进行大的改动。当前的方案已经符合要求。建议在相关设置界面中，为用户提供一个可编辑 `db.memorySettings.extractionPrompt` 内容的文本框，从而允许高级用户自定义记忆提取的行为，而无需修改任何代码。