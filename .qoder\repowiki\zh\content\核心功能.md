
# 核心功能

<cite>
**本文档引用的文件**  
- [js.js](file://src/js/js.js)
- [index.html](file://src/index.html)
- [css.css](file://src/styles/css.css)
</cite>

## 目录
1. [介绍](#介绍)
2. [项目结构](#项目结构)
3. [聊天系统](#聊天系统)
4. [记忆系统](#记忆系统)
5. [多媒体系统](#多媒体系统)
6. [个性化设置](#个性化设置)
7. [社交功能](#社交功能)
8. [常见问题与解决方案](#常见问题与解决方案)

## 介绍
AIChatBox 是一个功能丰富的虚拟聊天应用，支持多角色互动、多媒体消息、朋友圈社交、个性化主题和记忆系统。本文档全面介绍其核心功能，包括聊天流程、记忆管理、音乐播放、个性化设置和社交模块，为用户提供详尽的使用指南和开发参考。

## 项目结构
AIChatBox 项目采用简洁的前端架构，主要由 HTML、CSS 和 JavaScript 文件构成。`index.html` 作为入口文件，定义了所有页面的结构和模态框。`css.css` 负责全局样式和主题。`js.js` 是核心逻辑文件，包含了应用的所有功能实现，如聊天、记忆、音乐、设置等。

```mermaid
graph TB
A[index.html] --> B[css.css]
A --> C[js.js]
C --> D[localStorage]
D --> E[数据持久化]
```

**文档来源**
- [index.html](file://src/index.html#L1-L150)
- [js.js](file://src/js/js.js#L1-L8101)
- [css.css](file://src/styles/css.css#L1-L100)

## 聊天系统
聊天系统是 AIChatBox 的核心，支持消息的发送、接收、编辑、撤回和多选操作。用户可以与单个角色或群组进行互动，系统通过 `openChatRoom` 函数加载聊天界面，并通过 `sendMessage` 和 `getAiReply` 处理消息的发送与接收。

### 消息发送与接收流程
消息发送流程始于用户在输入框中输入文本并点击发送按钮。系统会创建一个包含时间戳、角色和内容的消息对象，并将其推入当前聊天的 `history` 数组。随后，`addMessageBubble` 函数将消息渲染到界面。对于 AI 回复，系统会根据角色或群组生成相应的系统提示词，并通过 `getAiReply` 调用 API 获取回复。

**Section sources**
- [js.js](file://src/js/js.js#L2400-L2450)
- [js.js](file://src/js/js.js#L3200-L3250)

### 多角色与群聊管理
应用支持创建和管理多个角色（伙伴）和群聊。用户可以通过“创建新伙伴”模态框添加角色，通过“创建群聊”模态框创建群组。群聊成员可以是现有角色或新创建的成员。群聊设置允许管理员修改群名、更换头像、管理成员权限。

```mermaid
classDiagram
class Character {
+id : string
+realName : string
+remarkName : string
+avatar : string
+persona : string
+history : Message[]
}
class Group {
+id : string
+name : string
+avatar : string
+members : Member[]
+history : Message[]
}
class Member {
+id : string
+originalCharId : string
+groupNickname : string
+isAdmin : boolean
}
class Message {
+id : string
+role : "user" | "assistant"
+content : string
+timestamp : number
}
Character --> Message : 发送
Group --> Message : 发送
Group --> Member : 包含
```

**Section sources**
- [js.js](file://src/js/js.js#L1200-L1300)
- [js.js](file://src/js/js.js#L4800-L4900)

### 消息类型处理
系统支持多种消息类型，包括文本、图片、语音、转账、红包、礼物、位置和音乐分享。每种消息类型都有特定的数据结构和渲染方式。例如，图片消息包含 `imageData` 对象，语音消息包含 `voice-bubble` 元素，转账消息包含 `transferStatus` 字段。

```mermaid
flowchart TD
A[用户操作] --> B{选择消息类型}
B --> C[文本]
B --> D[图片]
B --> E[语音]
B --> F[转账]
B --> G[红包]
B --> H[礼物]
B --> I[位置]
B --> J[音乐]
C --> K[调用 sendMessage]
D --> L[调用 sendImageMessage]
E --> M[调用 sendMyVoiceMessage]
F --> N[调用 sendMyTransfer]
G --> O[调用 sendMyRedPacket]
H --> P[调用 sendMyGift]
I --> Q[调用 sendMyLocation]
J --> R[调用 openShareMusicModal]
```

**Section sources**
- [js.js](file://src/js/js.js#L2700-L2800)
- [js.js](file://src/js/js.js#L3500-L3600)

### 多选操作机制
长按消息可进入多选模式。用户可以选中多条消息，然后执行删除、转发或收藏操作。`enterMultiSelectMode` 函数负责进入多选模式，`toggleMessageSelection` 函数处理消息的选中状态，`deleteSelectedMessages` 和 `forwardSelectedMessages` 函数执行最终操作。

**Section sources**
- [js.js](file://src/js/js.js#L2750-L2800)

## 记忆系统
记忆系统是 AIChatBox 的智能核心，它允许角色从对话中提取和注入记忆，从而实现更连贯和个性化的互动。

### 记忆提取与注入
记忆提取通过 `extractAndStoreMemory` 函数实现。该函数将角色的聊天历史和现有记忆作为输入，发送给 AI 模型，要求其提取新的记忆点。提取到的记忆会以 `{{memories}}` 的形式注入到角色的系统提示词中，影响其后续回复。

```mermaid
sequenceDiagram
participant User as 用户
participant App as 应用
participant AI as AI模型
User->>App : 发送消息
App->>App : 检查是否需要提取记忆
App->>AI : 发送提取提示词和聊天历史
AI-->>App : 返回新的记忆点
App->>App : 将新记忆点存入 memoryEntries
App->>AI : 发送包含记忆的系统提示词
AI-->>App : 返回个性化回复
App->>User : 显示回复
```

**Section sources**
- [js.js](file://src/js/js.js#L3150-L3200)
- [js.js](file://src/js/js.js#L3200-L3250)

### 长期记忆管理策略
记忆分为全局记忆和角色专属记忆。全局记忆对所有角色可见，角色专属记忆仅对该角色有效。用户可以在“记忆核心”界面查看、编辑和删除记忆。系统还支持为角色设置自动记忆提取频率。

**Section sources**
- [js.js](file://src/js/js.js#L3150-L3200)

## 多媒体系统
多媒体系统包括音乐播放器和动态岛显示，为用户提供丰富的视听体验。

### 音乐播放器控制逻辑
音乐播放器支持播放、暂停、上一首、下一首和播放模式切换。播放列表存储在 `db.musicPlaylist` 中，当前播放的歌曲索引为 `currentPlayingSongIndex`。`setupMusicApp` 函数初始化播放器，`applyMusicPlayerCustomization` 函数应用自定义设置。

**Section sources**
- [js.js](file://src/js/js.js#L5600-L5700)

### 歌单管理
用户可以通过“管理歌单”界面查看和管理播放列表。歌曲可以来自本地文件上传或 URL 添加。播放列表支持基本的增删操作。

**Section sources**
- [js.js](file://src/js/js.js#L5700-L5800)

### 动态岛显示机制
当有歌曲正在播放时，顶部会显示一个“动态岛”。用户可以点击动态岛展开播放器控件。`setupDynamicIsland` 函数负责处理动态岛的显示和隐藏逻辑。

```mermaid
stateDiagram-v2
[*] --> 隐藏
隐藏 --> 展开 : 点击动态岛
展开 --> 隐藏 : 点击外部或超时
展开 --> 折叠 : 点击播放/暂停
折叠 --> 展开 : 点击动态岛
```

**Section sources**
- [js.js](file://src/js/js.js#L5800-L5900)

## 个性化设置
用户可以自定义应用的外观和行为。

### 主题、字体、壁纸自定义
在“美化”界面，用户可以更换壁纸、设置全局主题颜色、应用自定义字体和更换主屏幕图标。这些设置会实时保存到 `localStorage` 并立即生效。

**Section sources**
- [js.js](file://src/js/js.js#L1400-L1500)

## 社交功能
社交功能模块实现了朋友圈的发布、评论和动态展示。

### 朋友圈发布与评论
用户可以发布包含文字和图片的动态。其他角色会根据设定自动发布或评论动态。`createAutomaticPost` 函数负责生成自动发布的内容，`triggerAiReactions` 函数模拟其他角色的点赞和评论。

**Section sources**
- [js.js](file://src/js/js.js#L6000-L6100)

### 动态展示
朋友圈动态按时间倒序排列，显示发布者、内容、图片和互动信息。用户可以点赞、评论或删除自己的动态。

**Section sources**
- [js.js](file://src/js/js.js#L6100-L6200)

## 常见问题与解决方案
- **问题：无法发送消息**
  - **解决方案**：检查 API 配置是否正确，确保已激活一个 API 配置。
- **问题：AI 没有回复**
  - **解决方案**：检查网络连接，确认 API 服务商是否正常工作。
- **问题：图片无法加载**
  - **解决方案**：检查图片 URL 是否有效，或尝试重新上传图片。
- **