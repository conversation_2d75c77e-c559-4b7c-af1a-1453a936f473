# 开发指南

<cite>
**Referenced Files in This Document**   
- [js.js](file://src/js/js.js)
- [css.css](file://src/styles/css.css)
- [index.html](file://src/index.html)
- [README.md](file://README.md)
</cite>

## 目录
1. [开发环境搭建](#开发环境搭建)
2. [代码结构与模块化设计](#代码结构与模块化设计)
3. [添加新功能](#添加新功能)
4. [修改现有模块](#修改现有模块)
5. [调试技巧与常见错误排查](#调试技巧与常见错误排查)
6. [代码风格与提交规范](#代码风格与提交规范)

## 开发环境搭建

### 浏览器兼容性要求
本项目为一个现代化的Web应用，建议在最新版本的主流浏览器中进行开发和测试，以确保最佳的兼容性和性能表现。推荐使用以下浏览器：
- **Google Chrome** (最新版)
- **Mozilla Firefox** (最新版)
- **Microsoft Edge** (最新版)
- **Safari** (最新版)

项目利用了现代Web API（如`localStorage`、`FileReader`、`fetch`等），因此不支持过时的浏览器（如IE系列）。

### 调试工具推荐
为了高效地开发和调试，推荐使用以下工具和方法：
1.  **浏览器开发者工具 (DevTools)**：这是最核心的工具，用于检查DOM结构、调试JavaScript、分析网络请求和性能。
2.  **`console.log()`**：在代码中关键位置插入`console.log()`语句，输出变量值和执行流程，是快速定位问题的基础方法。
3.  **`debugger`语句**：在代码中插入`debugger`关键字，当代码执行到此处时，浏览器会自动在开发者工具中暂停，方便进行断点调试。
4.  **`showToast()`函数**：项目内置了`showToast(message)`函数，可以在页面上显示一个短暂的提示信息，非常适合在移动端模拟环境中进行调试。例如，在`setupHomeScreen()`函数中，可以添加`console.log("