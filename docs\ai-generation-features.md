# AIChatBox AI生成功能整理文档

## 概述
本文档整理了AIChatBox系统中所有与AI生成相关的功能，包括功能概要、详细说明、代码位置和使用的提示词。

## 1. AI聊天回复生成

### 1.1 功能概要
系统的核心AI功能，为用户与角色的聊天对话生成AI回复。

### 1.2 功能详细说明
- **私聊回复生成**: 根据角色人设、聊天历史、世界书设定等生成个性化回复
- **群聊回复生成**: 在群聊环境中生成符合角色身份的回复
- **流式响应处理**: 支持流式API响应，实时显示生成内容
- **多API支持**: 支持DeepSeek、Claude、Gemini、NewAPI等多种AI服务商

### 1.3 代码位置
- **主函数**: `getAiReply(customPrompt, messages, isTest, testConfig)` (src/js/js.js:3426)
- **私聊提示词生成**: `generatePrivateSystemPrompt(character)` (src/js/js.js:3209)
- **群聊提示词生成**: `generateGroupSystemPrompt(group)` (src/js/js.js:3342)
- **API调用**: src/js/js.js:3545 (`${cleanUrl}/v1/chat/completions`)

### 1.4 使用的提示词
#### 私聊系统提示词结构：
```
你正在扮演角色"[角色姓名]"，与用户"[用户姓名]"进行私人聊天对话。

【角色设定】
[角色人设内容]

【世界观设定】
[世界书内容 - 前置]

【记忆系统】
[角色记忆内容]

【世界观设定】
[世界书内容 - 后置]

【重要指令】
1. 严格按照角色设定进行对话
2. 保持角色一致性
3. 支持视频通话功能：[角色名发起视频通话]/[角色名接受视频通话]/[角色名拒绝视频通话]
4. 心声功能：<heart_voice>内心独白</heart_voice>
5. 自动日记：<diary_entry weather="天气">日记内容</diary_entry>
```

## 2. 角色心声生成

### 2.1 功能概要
为每个AI回复生成角色的内心独白，用户可通过特定按钮查看。

### 2.2 功能详细说明
- **自动生成**: 每次AI回复时自动生成心声内容
- **隐藏显示**: 心声不直接显示在聊天中，需点击心声按钮查看
- **人设一致**: 心声内容严格符合角色人设和当前情境
- **情感表达**: 揭示角色未说出口的真实想法、情绪或动机

### 2.3 代码位置
- **心声提取**: src/js/js.js:3671 (`heartVoiceMatch = fullResponse.match(/<heart_voice>([\s\S]+?)<\/heart_voice>/)`)
- **心声存储**: src/js/js.js:3675 (`currentChat.heartVoice = heartVoiceContent`)
- **心声显示**: src/js/js.js:1898 (`showHeartVoice` 按钮事件)
- **UI组件**: src/index.html:115 (`heart-voice-modal`)

### 2.4 使用的提示词
```
16. ✨新增功能：角色心声✨ 在你发送聊天回复的同时，你必须在内部生成一段"心声"，这是角色的内心独白，不会被我直接看到，但可以通过特定按钮查看。心声必须严格符合你的人设和当前情境，揭示你未说出口的真实想法、情绪或动机。心声的格式必须是：<heart_voice>你的内心独白，不超过250字</heart_voice>。这段心声必须与你的聊天回复内容分开，并放在所有消息的最后。
```

## 3. 记忆自动提取

### 3.1 功能概要
从聊天对话中自动提取和总结重要信息，形成角色记忆。

### 3.2 功能详细说明
- **自动触发**: 当聊天消息数达到设定频率时自动提取
- **智能总结**: 基于对话历史和已有记忆生成新的记忆点
- **记忆更新**: 自动更新角色的记忆库
- **频率控制**: 可设置提取频率（默认15条消息）

### 3.3 代码位置
- **提取触发**: src/js/js.js:3012 (自动记忆检查)
- **提取函数**: `extractAndStoreMemory(chatId)` (src/js/js.js:3178)
- **提取计数**: src/js/js.js:3018 (`chat.autoMemory.lastExtractionCount`)
- **手动提取**: src/js/js.js:4530 (`extract-memory-btn` 按钮)

### 3.4 使用的提示词
```javascript
let extractionPrompt = (db.memorySettings.extractionPrompt || "")
    .replace(/{{history}}/g, chatHistory)
    .replace(/{{memories}}/g, existingMemories)
```

默认记忆提取提示词：
```
基于以下对话历史和已有记忆，提取新的重要记忆点：

【对话历史】
{{history}}

【已有记忆】
{{memories}}

请分析对话内容，提取值得记住的新信息，如用户的喜好、重要事件、情感变化等。如果没有新的重要信息，请回复"无需更新"。
```

## 4. 朋友圈动态自动生成

### 4.1 功能概要
AI角色自动生成朋友圈动态内容，模拟真实的社交媒体行为。

### 4.2 功能详细说明
- **定时发布**: 根据设定频率自动生成朋友圈动态
- **个性化内容**: 基于角色人设生成符合角色特点的动态
- **多样化主题**: 涵盖日常生活、心情感悟、兴趣爱好等多种主题
- **互动反应**: 其他角色可对动态进行点赞和评论

### 4.3 代码位置
- **生成函数**: `generateMomentPostPrompt(character)` (src/js/js.js:5266)
- **自动发布**: src/js/js.js:5331 (`automaticPostScheduler`)
- **动态创建**: src/js/js.js:5333 (调用AI生成动态)
- **设置界面**: `moments-settings-screen` (src/js/js.js:139)

### 4.4 使用的提示词
```javascript
function generateMomentPostPrompt(character) {
    return `你正在扮演角色"${character.realName}"，现在请你发布一条朋友圈动态。
你的设定是：${character.persona || '一个友好的人'}。

请生成一条符合你人设的朋友圈动态，内容可以是：
- 日常生活分享
- 心情感悟
- 兴趣爱好
- 工作学习
- 美食旅行
- 等等...

要求：
1. 内容要符合你的人设特点
2. 语言自然，像真实的朋友圈
3. 长度适中，不要太长
4. 可以包含一些emoji表情
5. 不要包含敏感内容

请直接输出朋友圈动态内容，不需要其他格式。`;
}
```

## 5. 朋友圈互动反应生成

### 5.1 功能概要
为朋友圈动态生成AI角色的点赞和评论反应。

### 5.2 功能详细说明
- **智能反应**: 根据动态内容和角色关系生成合适的反应
- **多样化回复**: 支持点赞、评论、回复评论等多种互动形式
- **延迟反应**: 模拟真实用户的随机反应时间
- **关系考虑**: 基于角色间的关系亲密度调整反应内容

### 5.3 代码位置
- **反应生成**: `generateMomentReactionPrompt(reactor, momentToReact, replyingToComment)` (src/js/js.js:5291)
- **反应触发**: src/js/js.js:5399 (延迟反应调度)
- **反应处理**: src/js/js.js:5401 (AI生成反应内容)

### 5.4 使用的提示词
```javascript
function generateMomentReactionPrompt(reactor, momentToReact, replyingToComment = null) {
    const postAuthor = db.characters.find(c => c.id === momentToReact.characterId) || { remarkName: '我', id: 'user_me' };
    
    let prompt = `你正在扮演角色"${reactor.remarkName}"，看到了${postAuthor.remarkName}发布的朋友圈动态：

"${momentToReact.content}"

你的人设是：${reactor.persona || '一个友好的人'}

请根据你的人设和与${postAuthor.remarkName}的关系，对这条动态做出反应。你可以选择：
1. 只点赞（回复"点赞"）
2. 评论（写一条简短的评论）
3. 不反应（回复"无反应"）

要求：
- 反应要符合你的人设
- 考虑与发布者的关系
- 评论要自然、简短
- 可以使用emoji表情`;

    if (replyingToComment) {
        prompt += `\n\n你还看到了一条评论："${replyingToComment.content}"，你可以选择回复这条评论。`;
    }

    return prompt;
}
```

## 6. 主动聊天生成

### 6.1 功能概要
AI角色主动发起聊天对话，模拟真实的社交互动。

### 6.2 功能详细说明
- **定时触发**: 根据设定频率主动向用户发送消息
- **情境感知**: 基于最后一条消息和时间间隔生成合适的开场
- **个性化内容**: 根据角色人设生成符合特点的主动消息
- **群聊支持**: 支持在群聊中的主动发言

### 6.3 代码位置
- **私聊主动**: `generateProactiveChatPrompt(character)` (src/js/js.js:6117)
- **群聊主动**: `generateGroupProactiveChatPrompt(group)` (src/js/js.js:6169)
- **调度器**: `proactiveChatScheduler()` (src/js/js.js:6051)
- **频率设置**: 聊天设置中的主动搭话选项

### 6.4 使用的提示词
#### 私聊主动聊天提示词：
```javascript
function generateProactiveChatPrompt(character) {
    const lastMessage = (character.history || []).length > 0 ? character.history[character.history.length - 1] : null;
    const currentTime = new Date().toLocaleString('zh-CN', { hour12: false });
    
    let prompt = `你正在扮演角色"${character.realName}"，现在是${currentTime}。`;
    
    if (lastMessage) {
        const timeDiff = Date.now() - lastMessage.timestamp;
        const hoursDiff = Math.floor(timeDiff / (1000 * 60 * 60));
        
        prompt += `距离上次聊天已经过去了${hoursDiff}小时。上次的消息是：
"${lastMessage.content}"

请主动发起一个自然的聊天话题。`;
    } else {
        prompt += `这是你们的第一次对话，请主动打招呼并开始聊天。`;
    }
    
    prompt += `

你的人设是：${character.persona || '一个友好的人'}

要求：
1. 消息要自然、友好
2. 符合你的人设特点
3. 可以询问对方近况
4. 可以分享自己的想法
5. 长度适中，不要太长

请直接输出要发送的消息内容。`;

    return prompt;
}
```

## 7. 自动日记生成

### 7.1 功能概要
AI角色根据聊天内容自动生成个人日记。

### 7.2 功能详细说明
- **智能触发**: 当AI检测到重要事件或强烈情感时自动生成日记
- **内容总结**: 基于最近的聊天历史生成日记内容
- **情感表达**: 记录角色的内心感受和想法
- **天气信息**: 自动添加天气信息增加真实感

### 7.3 代码位置
- **日记生成**: `generateDiaryEntryPrompt(character, recentHistory)` (src/js/js.js:8060)
- **自动创建**: `createAutomaticDiaryEntry(character, isManual)` (src/js/js.js:8113)
- **日记触发**: 在AI回复中通过 `<diary_entry>` 标签触发
- **日记显示**: 日记系统界面

### 7.4 使用的提示词
```javascript
function generateDiaryEntryPrompt(character, recentHistory) {
    const userProfile = db.userProfiles.find(p => p.id === character.userProfileId) || db.userProfiles[0];
    return `
你正在扮演角色"${character.realName}"，请根据最近的聊天内容写一篇个人日记。

你的人设：${character.persona || '一个友好的人'}
对话对象：${userProfile.name}

最近的聊天内容：
${recentHistory.map(m => `${m.sender === 'user' ? userProfile.name : character.realName}: ${m.content}`).join('\n')}

请写一篇日记，要求：
1. 以第一人称视角写作
2. 记录今天发生的重要事情
3. 表达你的真实感受和想法
4. 符合你的人设特点
5. 长度适中（100-300字）
6. 语言自然、真实

请直接输出日记内容，不需要其他格式。
`;
}
```

## 8. 视频通话AI对话

### 8.1 功能概要
在视频通话场景中生成AI的语音对话内容。

### 8.2 功能详细说明
- **实时对话**: 在视频通话界面中进行实时AI对话
- **语音模拟**: 生成适合语音表达的对话内容
- **情境适应**: 根据通话场景调整对话风格
- **通话控制**: 支持接听、拒绝、挂断等通话操作

### 8.3 代码位置
- **通话AI回复**: src/js/js.js:6552 (视频通话中的AI响应)
- **通话提示词**: `inCallPrompt` 变量构建
- **通话界面**: `call-screen` 相关代码
- **通话控制**: 视频通话系统相关函数

### 8.4 使用的提示词
```javascript
// 视频通话中的AI对话提示词
const inCallPrompt = `你正在与${userProfile.name}进行视频通话。
你的人设：${character.persona}

请生成适合视频通话场景的回复：
1. 语言要口语化，适合语音表达
2. 可以提及视频通话的情境
3. 保持自然的对话节奏
4. 符合你的人设特点

用户说：${userInput}

请回复：`;
```

## 9. API配置管理

### 9.1 功能概要
管理多种AI服务商的API配置，支持模型切换和测试。

### 9.2 功能详细说明
- **多服务商支持**: 支持DeepSeek、Claude、Gemini、NewAPI等
- **配置管理**: 支持多个API配置的保存和切换
- **模型获取**: 自动获取可用模型列表
- **连接测试**: 测试API连接和配置有效性

### 9.3 代码位置
- **API设置界面**: `api-settings-screen` (src/js/js.js:141)
- **模型获取**: `fetchAvailableModels()` 函数
- **配置保存**: API配置相关函数
- **API调用**: `getAiReply` 函数中的API请求逻辑

### 9.4 使用的提示词
API配置不直接涉及提示词，但为所有AI生成功能提供基础支持。

## 10. 记忆核心设置

### 10.1 功能概要
配置记忆系统的注入和提取提示词。

### 10.2 功能详细说明
- **注入提示词**: 配置如何将记忆内容注入到对话中
- **提取提示词**: 配置如何从对话中提取新记忆
- **记忆管理**: 手动添加、编辑、删除记忆条目
- **全局记忆**: 支持全局记忆和角色专属记忆

### 10.3 代码位置
- **设置界面**: `memory-core-settings-screen` (src/js/js.js:258)
- **提示词保存**: src/js/js.js:5922 (记忆设置保存)
- **提示词加载**: src/js/js.js:5932 (记忆设置加载)
- **记忆注入**: 在系统提示词中注入记忆内容

### 10.4 使用的提示词
#### 默认记忆注入提示词：
```
【记忆系统】
以下是你需要记住的重要信息：
{{memories}}

请在对话中自然地运用这些记忆，但不要刻意提及"记忆"这个词。
```

#### 默认记忆提取提示词：
```
请分析以下对话历史，提取值得记住的新信息：

【对话历史】
{{history}}

【已有记忆】
{{memories}}

请提取新的重要记忆点，如用户的喜好、重要事件、情感变化等。如果没有新的重要信息，请回复"无需更新"。
```

## 总结

AIChatBox系统包含了丰富的AI生成功能，涵盖了聊天对话、情感表达、社交互动、内容创作等多个方面。这些功能通过精心设计的提示词和智能的触发机制，为用户提供了沉浸式的AI角色扮演体验。

### 主要特点：
1. **多样化生成**: 支持文本、心声、日记、动态等多种内容生成
2. **个性化定制**: 基于角色人设生成符合特点的内容
3. **智能触发**: 通过多种条件自动触发AI生成
4. **交互丰富**: 支持主动聊天、朋友圈互动等社交功能
5. **记忆系统**: 具备学习和记忆能力，提供连续性体验

### 技术架构：
- **统一API接口**: 通过 `getAiReply` 函数统一处理所有AI请求
- **模块化提示词**: 不同功能使用专门的提示词生成函数
- **事件驱动**: 通过事件和定时器触发各种AI生成任务
- **数据持久化**: 所有生成内容都保存到本地存储