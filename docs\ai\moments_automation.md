# AI功能文档：朋友圈自动化

## 1. 功能概述

### 核心目标
让AI角色拥有在“朋友圈”中独立生活的能力。它们不仅能根据自身人设自动发布动态，还能像真人一样浏览、点赞和评论其他角色（包括用户）发布的动态，从而构建一个生动、可信的虚拟社交生态。

### 典型应用场景
-   **自动发布**: AI角色“小狗”可能会在深夜自动发布一条朋友圈：“今天有点emo...”，符合它敏感的人设。
-   **自动互动**: 用户发布了一条关于猫咪的动态后，AI角色“小猫”可能会在几分钟后自动点赞并评论：“好可爱！和我一样可爱！”。

## 2. 实现细节

### 完整逻辑流程

#### A. 自动发布动态
1.  系统通过 `setInterval` 定期（每5分钟）触发 `automaticPostScheduler` 函数。
2.  函数检查朋友圈的自动发布功能是否开启 (`db.momentsSettings.enabled`)。
3.  根据设定的发布频率 (`high`, `medium`, `low`) 和上次发布时间，计算出一个随机的时间间隔。
4.  如果当前时间与上次发布时间的差值超过了该间隔，则触发发布流程。
5.  系统随机选择一个AI角色。
6.  调用 `generateMomentPostPrompt(character)` 函数，为该角色构建一个用于生成朋友圈文案的提示词。
7.  调用 `getAiReply(prompt)` 获取AI生成的动态内容。AI的回复是一个JSON字符串，可能包含文案、图片生成提示或音乐链接。
8.  `createAutomaticPost` 函数解析JSON，创建一个新的动态对象，并将其添加到 `db.moments` 数组的开头。
9.  更新 `db.momentsSettings.lastPostTime`，并调用 `saveData()` 持久化。
10. 如果用户不在朋友圈界面，会通过 `showTopNotification` 显示一条新动态通知。
11. **关键联动**: 发布成功后，会通过 `setTimeout` 延迟几秒钟，调用 `triggerAiReactions()` 来模拟其他AI角色对这条新动态的反应。

#### B. 自动互动（点赞/评论）
1.  当一条新动态被创建时（无论是用户还是AI发布的），会调用 `triggerAiReactions(momentId)` 函数。
2.  函数首先确定哪些AI角色有资格进行互动（例如，不能是发布者自己，且之前未互动过）。
3.  对于每个有资格的“围观”AI角色，系统会随机延迟几秒到十几秒，以模拟真实的反应时间。
4.  延迟结束后，为该AI角色调用 `generateMomentReactionPrompt()` 函数，构建一个用于决定如何反应（点赞、评论、或忽略）的提示词。
5.  该提示词包含了动态的原文、已有的评论，以及互动双方的关系（朋友或陌生人），要求AI做出决策。
6.  调用 `getAiReply(prompt)` 获取AI的决策。AI的回复是特定格式的指令，如 `[LIKE]`, `[COMMENT:你的评论内容]`, `[LIKE][COMMENT:...]` 或 `[IGNORE]`。
7.  系统解析指令，更新 `db.moments` 中对应动态的点赞列表或评论列表。
8.  如果AI评论或点赞了用户的动态，会触发 `showTopNotification` 通知用户。

### 输入输出规范
-   **输入**:
    -   `db.momentsSettings` 中的配置。
    -   `db.characters` 中的角色人设。
    -   `db.moments` 中已有的动态和评论。
-   **输出**:
    -   AI模型输出JSON格式的动态内容或指令格式的互动决策。
    -   最终在 `db.moments` 中创建新动态或更新现有动态的点赞/评论数据。

### 异常处理
-   如果AI返回的动态内容或互动指令格式不正确，该次操作会被忽略。
-   API请求错误由 `getAiReply` 的通用异常处理机制负责。

## 3. 代码位置

-   **自动发布调度器**: `src/js/js.js#L5873-5886` (函数 `automaticPostScheduler`)
-   **自动发布实现**: `src/js/js.js#L5933-5965` (函数 `createAutomaticPost`)
-   **自动互动触发器**: `src/js/js.js#L5967-6063` (函数 `triggerAiReactions`)
-   **动态生成提示词**: `src/js/js.js#L5888-5906` (函数 `generateMomentPostPrompt`)
-   **互动决策提示词**: `src/js/js.js#L5908-5931` (函数 `generateMomentReactionPrompt`)

## 4. 提示词模板

提示词的核心指令硬编码在对应的生成函数中。

### 动态发布提示词 (`generateMomentPostPrompt`)
```
你正在扮演角色“{{character.realName}}”，现在请你发布一条朋友圈动态。
你的设定是：{{character.persona}}。
你的动态应该符合你的角色设定，可以是对日常生活的记录、一个想法、一张照片的描述等等。

你的任务是生成动态的文案，并决定是否需要配图或配乐。
请严格按照以下JSON格式之一进行回复，不要添加任何额外的解释或Markdown标记：

1.  **只有文字**：
    `{"content": "你的动态文案"}`

2.  **文字和图片（AI生成）**：
    `{"content": "你的动态文案", "image_prompt": "用于AI绘画的英文关键词"}`

3.  **文字和音乐**：
    `{"content": "你的动态文案", "music_url": "音乐文件的URL"}`

【注意】请根据你的人设和动态内容，合理决定是否需要图片或音乐。大部分情况下，只发文字动态即可。
```

### 互动决策提示词 (`generateMomentReactionPrompt`)
```
你正在扮演角色“{{reactor.realName}}”，你的设定是：{{reactor.persona}}。
你正在看“{{postAuthor.remarkName}}”的朋友圈动态。
...
--- 动态内容 ---
{{postAuthor.remarkName}}: {{momentToReact.content}}
--- 已有评论 ---
...
--- 你的任务 ---
现在，请决定你的行动。你有四个选择：
1. 点赞和评论: ...[LIKE][COMMENT:你的评论内容]
2. 只评论: ...[COMMENT:你的评论内容]
3. 只点赞或忽略: ...[LIKE] 或 [IGNORE]
...
```

## 5. 存储方案

与项目中的其他AI功能类似，这些提示词模板目前硬编码在JavaScript代码中。为了提高灵活性，建议将这些模板字符串移至 `db.promptSettings` 中进行统一管理，并提供UI界面供用户修改。