<cite>
**Referenced Files in This Document**   
- [index.html](file://src/index.html)
- [js.js](file://src/js/js.js)
- [css.css](file://src/styles/css.css)
- [README.md](file://README.md)
</cite>

## 目录
1. [文件说明](#文件说明)
2. [index.html 文件说明](#indexhtml-文件说明)
3. [js.js 文件说明](#jsjs-文件说明)
4. [css.css 文件说明](#csscss-文件说明)
5. [README.md 文件说明](#readmemd-文件说明)

## 文件说明

本文档旨在详细说明 AIChatBox 项目中的核心文件，包括 `index.html`、`js.js`、`css.css` 和 `README.md`。每个文件都承担着不同的职责，共同构建了一个功能完整的 AI 聊天应用。`index.html` 作为主页面，定义了应用的整体结构和模态框；`js.js` 是核心逻辑文件，负责状态管理、DOM 操作和事件处理；`css.css` 定义了全局样式和视觉风格，并支持主题切换；`README.md` 则提供了项目的基本信息和使用指南。

## index.html 文件说明

`index.html` 是应用的主页面，定义了整个应用的 HTML 结构和布局。它包含了所有屏幕（如聊天列表、聊天室、音乐播放器等）的容器，以及各种模态框（如添加伙伴、编辑资料、发送消息等）的定义。页面结构清晰，通过 `div` 元素的 `id` 属性来区分不同的功能模块。

该文件通过 `<link>` 标签引入了 `css.css` 样式表，并通过 `<script>` 标签引入了 `js.js` 脚本文件，实现了样式与逻辑的分离。`index.html` 中定义了大量的 `div` 元素，这些元素在 `js.js` 中通过 JavaScript 动态注入 HTML 内容，实现了单页应用（SPA）的效果。

**Section sources**
- [index.html](file://src/index.html#L1-L150)

## js.js 文件说明

`js.js` 是应用的核心逻辑文件，负责处理所有交互和数据管理。它包含了模块初始化函数、状态管理逻辑、DOM 操作和事件监听器的实现。文件首先定义了一些工具函数，如 `compressImage` 用于压缩图片，`adjustColor` 用于调整颜色。

文件的核心是 `injectHTML` 函数，它动态地向各个屏幕容器中注入 HTML 内容，实现了界面的动态生成。全局变量和常量定义了应用的状态，如 `db` 对象用于存储数据，`currentChatId` 用于跟踪当前聊天会话。`loadData` 和 `saveData` 函数负责数据的持久化，使用 `localStorage` 来保存和读取数据。

事件监听器通过 `document.addEventListener` 和 `getEl` 函数来绑定，确保了 DOM 元素的事件能够被正确处理。`switchScreen` 函数用于在不同屏幕之间切换，实现了单页应用的导航功能。此外，文件还定义了 `createContextMenu` 和 `removeContextMenu` 函数，用于处理右键菜单的显示和隐藏。

**Section sources**
- [js.js](file://src/js/js.js#L1-L800)

## css.css 文件说明

`css.css` 文件定义了应用的全局样式规则和视觉风格。它使用 CSS 变量（如 `--primary-color`、`--secondary-color`）来定义主题颜色，使得主题切换变得简单。文件通过 `:root` 选择器定义了这些变量，可以在 JavaScript 中动态修改，从而实现全局主题的实时更新。

样式表包含了对各种 UI 组件的详细定义，如 `.app-header` 定义了应用头部的样式，`.message-bubble` 定义了消息气泡的样式。`.phone-screen` 类定义了手机屏幕的外观，包括圆角、阴影和背景渐变。`.modal-overlay` 和 `.modal-window` 类定义了模态框的样式，实现了半透明背景和居中显示的效果。

文件还使用了 `@keyframes` 规则来定义动画，如 `fadeInModal` 和 `slideUp`，为模态框的显示和隐藏提供了平滑的过渡效果。`.btn` 类定义了按钮的样式，采用了毛玻璃（glassmorphism）效果，通过 `backdrop-filter: blur(10px)` 实现了背景模糊。

**Section sources**
- [css.css](file://src/styles/css.css#L1-L800)

## README.md 文件说明

`README.md` 文件是项目的说明文档，包含了项目的基本信息、使用说明和配置指南。它使用 Markdown 语法编写，结构清晰，易于阅读。文件首先通过 `#` 标题定义了项目名称，然后提供了简短的项目描述。

虽然当前 `README.md` 内容较为简单，仅包含项目名称和一句描述，但它为项目的文档化提供了基础。未来可以在此文件中添加更多内容，如安装步骤、配置选项、API 文档和贡献指南，以便其他开发者能够快速理解和使用该项目。

**Section sources**
- [README.md](file://README.md#L1-L3)